const mongoose = require('mongoose');

const CallbackResultSchema = new mongoose.Schema(
  {
    requestId: { type: String, ref: 'Request' },
    statusCode: String, // status code from response
    callbackUrl: String, // callback url from request
    index: Number, // number of api call retry
    payload: {}, // data sent to callbackUrl
    result: {}, // data received from callbackUrl
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

module.exports = mongoose.model('CallbackResult', CallbackResultSchema);
