const mongoose = require('mongoose')
import { REQUEST_TYPE } from '../constants'

const awsZoneSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    region: { type: String, required: true },
    weight: { type: Number, required: true },
    allowRequestTypes: [{ type: String, enum: Object.values(REQUEST_TYPE) }],
    normalizerFunction: { type: String },
    sentenceTokenizerFunction: { type: String },
    newSentenceTokenizerFunction: { type: String },
    textToAllophoneFunction: { type: String },
    synthesisFunction: { type: String },
    joinSentencesFunction: { type: String },
    bucketS3: { type: String },
  },
  {
    versionKey: false,
    timestamps: true,
  },
)

module.exports = mongoose.model('AwsZone', awsZoneSchema)
