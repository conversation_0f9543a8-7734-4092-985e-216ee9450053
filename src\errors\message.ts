const codes = require('./code');

const getErrorMessage = (code) => {
  switch (code) {
    case codes.INVALID_VOICE_CODE:
      return 'Invalid voice code';
    case codes.TEXT_TOO_LONG:
      return 'Text too long';
    case codes.USER_NOT_FOUND:
      return 'User is not found';
    case codes.PACKAGE_EXPIRED:
      return 'Package has expired';
    case codes.REQUEST_NOT_FOUND:
      return 'Request is not found';
    case codes.INVALID_SYNTAX:
      return 'Invalid syntax';
    case codes.PACKAGE_NOT_EXIST:
      return 'Package is not exist';
    case codes.TTS_FAILURE:
      return 'Convert TTS failed';
    case codes.REQUEST_TIMEOUT:
      return 'Request timeout';
    case codes.UNAVAILABLE_VOICE:
      return 'Voice is unavailable';
    case codes.APP_NOT_FOUND:
      return 'App is not found';
    case codes.APP_NOT_ACTIVATED:
      return 'App is not activated';
    case codes.IS_HATE_SPEECH:
      return 'Request is a hate speech';
    case codes.BLACK_WORD_EXIST:
      return 'This black word exists';
    case codes.BLACK_WORD_NOT_EXIST:
      return 'This black word does not exist';
    case codes.SENTENCES_NOT_SUPPORT_EMPHASIS:
      return 'Convert by sentences not support emphasis';
    case codes.INVALID_AUDIO_TYPE:
      return 'Invalid audio type, only support mp3 and wav';
    case codes.FILE_TOO_LARGE:
      return 'File too large';
    case codes.TTS_CACHING_NOT_SUPPORT_THIS_VOICE:
      return 'TTS caching not support this voice';
    case codes.EXCEED_MAX_PREVIEW:
      return `Exceed max preview `;
    case codes.AWS_ZONE_EXIST:
      return 'Aws zone exist';
    case codes.USER_BLOCK:
      return 'User has been locked';
    case codes.EXCEED_PREVIEW_TTS:
      return 'User has exceed preview';
    case codes.DEMO_FEATURE_UNSUPPORTED:
      return 'Demo feature unsupported';
    case codes.VOICE_NOT_SUPPORT_DUBBING:
      return 'This voice does not support dubbing feature';
    case codes.TIMEOUT:
      return 'Timeout';
    case codes.DUBBING_AUDIO_TOO_LONG:
      return 'Dubbing audio too long';
    case codes.DUBBING_ONLY_SUPPORT_VIETNAMESE:
      return 'Dubbing feature only support vietnamese text';
    case codes.DUBBING_SENTENCE_TOO_LONG:
      return 'Dubbing sentence too long';

    case codes.VOICE_EXIST:
      return 'Voice is already exist';
    default:
      return null;
  }
};

module.exports = getErrorMessage;
