import requestDao from '../daos/request';
import Caching from './caching';
import { REDIS_KEY_PREFIX, REDIS_KEY_TTL, REQUEST_STATUS } from '../constants';

const CustomError = require('../errors/CustomError');
const code = require('../errors/code');
const { createInProgressRequest } = require('../daos/inProgressRequest');
const { getSynthesisTimeInRedis } = require('../daos/tts');

const { createRequestContent } = require('../daos/requestContent');

/** create and save a combo of RequestDTO, InProgressRequestDTO, RequestContentDTO to DB */
const createRequest = async (requestInfo) => {
  requestInfo = { ...requestInfo };
  requestInfo._id = requestInfo.requestId;
  const { text, sentences } = requestInfo;
  delete requestInfo.requestId;
  delete requestInfo.text;

  const request = await requestDao.createRequest(requestInfo);

  const { _id, createdAt } = requestInfo;
  await createInProgressRequest(_id, createdAt);
  await createRequestContent({ requestId: _id, text, sentences });

  return request;
};

const createRequestInRedis = async (requestInfo) => {
  requestInfo = { ...requestInfo };
  if (!requestInfo._id) requestInfo._id = requestInfo.requestId;
  delete requestInfo.requestId;

  const requestKey = `${REDIS_KEY_PREFIX.REQUEST}_${requestInfo._id}`;

  await Caching.RedisRepo.set(requestKey, JSON.stringify(requestInfo), REDIS_KEY_TTL.SYNTHESIS_REQUEST);
};

const saveSynthesisTime = async (requestId, startTime) => {
  const { t2aDurations, synthesisDurations } = await getSynthesisTimeInRedis(requestId);

  const getMinMaxAverageValueOfArray = (array) => {
    const min = array.length ? Math.min(...array) : 0;
    const max = array.length ? Math.max(...array) : 0;
    const arrayLength = array.length || 1;
    const average = array.reduce((a, b) => a + b, 0) / arrayLength;
    return { min, max, average: average.toFixed(3) };
  };
  const {
    min: minT2aDuration,
    max: maxT2aDuration,
    average: averageT2aDuration,
  } = getMinMaxAverageValueOfArray(t2aDurations);
  const {
    min: minSynthesisDuration,
    max: maxSynthesisDuration,
    average: averageSynthesisDuration,
  } = getMinMaxAverageValueOfArray(synthesisDurations);

  await requestDao.updateRequestByIdInRedis(requestId, {
    t2aDuration: {
      min: minT2aDuration,
      max: maxT2aDuration,
      average: averageT2aDuration,
    },
    synthesisDuration: {
      min: minSynthesisDuration,
      max: maxSynthesisDuration,
      average: averageSynthesisDuration,
      start: startTime,
      end: Date.now(),
    },
  });
};

const getRequestFromSessionId = async (sessionId) => {
  const selectFields = [
    'app',
    'characters',
    'voiceCode',
    'audioType',
    'speed',
    'bitrate',
    'progress',
    'createdAt',
    'status',
    'audioLink',
    'retentionPeriod',
    'error',
    'errorCode',
  ];

  // Find the request in the database
  const requestDB = await requestDao.findRequest({ sessionId }, selectFields);

  // Throw an error if the request is not found
  if (!requestDB) throw new CustomError(code.REQUEST_NOT_FOUND);

  // If the request is in progress, get it from the cache
  const request =
    requestDB.status === REQUEST_STATUS.IN_PROGRESS
      ? await requestDao.findRequestByIdInRedis(requestDB._id)
      : requestDB;

  // Destructure the request object
  const {
    app: appId,
    characters,
    voiceCode,
    audioType,
    speed,
    bitrate,
    progress,
    createdAt,
    status,
    audioLink,
    retentionPeriod,
  } = request;

  // Construct the result object using shorthand notation
  const result = {
    appId,
    requestId: request._id,
    characters,
    voiceCode,
    audioType,
    progress,
    speedRate: speed,
    bitrate,
    createdAt,
    status,
    audioLink,
    retentionPeriod,
  };

  return result;
};

const changeDomainUrl = (originalUrl, proxyDomain) => {
  const urlParts = originalUrl.split('/');

  // Find the domain part and replace it with the new one
  urlParts[2] = proxyDomain;

  // Join the URL parts back together
  const newUrl = urlParts.join('/');

  return newUrl;
};

/** create request title from its text */
const createRequestTitle = (text) => text?.slice(0, 30).trim() || 'untitled';

module.exports = {
  createRequest,
  createRequestInRedis,

  saveSynthesisTime,
  getRequestFromSessionId,
  changeDomainUrl,

  // static helpers
  createRequestTitle,
};
