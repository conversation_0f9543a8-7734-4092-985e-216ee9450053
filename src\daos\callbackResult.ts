const CallbackResult = require('../models/callbackResult');
const daoUtils = require('./utils');

const createCallbackResult = async (callbackResult) => {
  const result = await CallbackResult.create(callbackResult);
  return result;
};

const findCallbackResult = async (requestId, selectFields) => {
  const result = await await daoUtils.findOne(
    CallbackResult,
    { requestId },
    selectFields,
  );
  return result;
};

module.exports = { createCallbackResult, findCallbackResult };
