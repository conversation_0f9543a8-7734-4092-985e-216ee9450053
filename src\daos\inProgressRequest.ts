const InProgressRequest = require('../models/inProgressRequest');

/** store a new InProgressRequest DTO to DB */
const createInProgressRequest = async (requestId, createdAt) => {
  const inProgressRequestExist = await InProgressRequest.findById(requestId);
  if (inProgressRequestExist) return;

  await InProgressRequest.create({ _id: requestId, createdAt });
};

const deleteInProgressRequest = async (id) => {
  await InProgressRequest.findByIdAndDelete(id);
};

module.exports = { createInProgressRequest, deleteInProgressRequest };
