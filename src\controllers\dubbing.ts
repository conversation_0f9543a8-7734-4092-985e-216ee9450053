const dubbingService = require('../services/dubbing');

const handleDubbing = async (req, res) => {
  const { apiApp: app } = req;
  const { publicIP: ip } = req.__clientInfo;

  const {
    title,
    speedRate,
    subtitleLink,
    responseType,
    callbackUrl,
    callbackUpdateProgressUrl,
    voiceCode,
    outputType,
    audioType,
    bitrate,
    sampleRate,
    clientUserId,
    retentionPeriod,
    serviceType,
    sessionId,
    synthesisCcr,
    sentencesVoiceCode,
    synthesisComputePlatform,
  } = req.body;

  const request = await dubbingService.handleDubbing({
    ip,
    app,
    responseType,
    outputType,
    callbackUrl,
    callbackUpdateProgressUrl,
    title,
    speedRate,
    bitrate,
    voiceCode,
    audioType,
    subtitleLink,
    sampleRate,
    serviceType,
    clientUserId,
    retentionPeriod,
    sessionId,
    synthesisCcr,
    sentencesVoiceCode,
    synthesisComputePlatform,
  });

  return res.send(request);
};

module.exports = { handleDubbing };
