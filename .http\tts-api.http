@baseUrl = http://localhost:8010
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE2Nzc2NTQ0MDB9.YwO9ipHG-GtyyhkfcEKAr0Ym4K358j1nfMKZkl8KdIY

@AI_VOICE_APP_ID = 13c4e7df-a693-4cbe-8803-382839095410

Các tham số truyền vào có thể xem trong phần validation của API này ạ.

    const ttsPayload = {
      ...commonPayload,

      inputText: text,
      sentences: text ? null : newSentences,
      backgroundMusic: { link, volume },
      dictionary: dictionary?.words || null,
      // ADVISE: user can use clientPause or not, should prepare in the request beforehand
      clientPause: studioUsageOptions?.features?.includes(
        PACKAGE_FEATURE.CLIENT_PAUSE,
      )
        ? { paragraphBreak, sentenceBreak, majorBreak, mediumBreak }
        : {},
      returnTimestamp: returnTimestampWords,
      isPriority: isPriority || undefined,
      isRealTime: isRealTime || undefined,
      synthesisComputePlatform,
    };

    const dubbingPayload = {
      ...commonPayload,

      subtitleLink,
      sentencesVoiceCode,
      synthesisComputePlatform,
    };

luồng thì anh xem tại vbee-tts-api
  src/service/ttsProcessing
    function callApiSynthesis // Đây là function gọi sang tts-api

Các test case có thể tạo bằng cách từ giao diện anh tạo request chuyển văn bản hoặc chuyển phụ đề, lấy curl đó và gọi vào localhost vbee-tts-api,
sau đó log payload ở function callApiSynthesis


### Create TTS via vbee-tts-API to tts-API
POST {{baseUrl}}/api/v1/tts
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "Title Xin chào tôi là Unit test",
  "appId": "{{AI_VOICE_APP_ID}}",
  "callbackUrl": "http://localhost:8000/api/v1/tts/callback-tts-gate",
  "callbackUpdateProgressUrl": null,
  "responseType": "indirect",
  "audioType": "mp3",
  "bitrate": 128,
  "sampleRate": 22050,
  "voiceCode": "n_hn_male_duyonyx_oaistable_vc",
  "retentionPeriod": 1,
  "clientUserId": "f3c41af8-226e-478e-bb9a-0400c1e6e428",
  "sessionId": "6dd7c32e-749f-41fe-8908-bd15e805f07f",
  "speedRate": 1,
  "synthesisCcr": 1,
  "inputText": "Xin chào tôi là kiểm thử đơn vị",
  "sentences": null,
  "dictionary": null,
  "returnTimestamp": false,

  "synthesisComputePlatform": "CLOUD_RUN"
}

# "clientPause": {
#     "paragraphBreak": undefined,
#     "sentenceBreak": undefined,
#     "majorBreak": undefined,
#     "mediumBreak": undefined
#   },

#  "backgroundMusic": { "link": undefined, "volume": 80 },

#    "isPriority": undefined,
#    "isRealTime": undefined,

