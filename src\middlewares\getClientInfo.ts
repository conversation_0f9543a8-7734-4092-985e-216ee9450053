const { HttpProtocolHelper } = require('@gurucore/lakdak');

/** decorate the request with extra ClientInfo (ip, physical location) of browser/client */
const getClientInfo = (req, res, next) => {
  const { headers } = req;
  const clientInfo = HttpProtocolHelper.extractClientInfo(headers);

  // When naming information attached to an HttpRequest in middleware for later use by subsequent middleware or controllers, a good approach focuses on clarity, consistency, and avoiding potential conflicts.
  Object.assign(req, { __clientInfo: clientInfo });

  return next();
};

module.exports = getClientInfo;
