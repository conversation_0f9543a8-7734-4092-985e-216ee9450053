require('dotenv').config()
const { initMongoDB } = require('../../models')
const Voice = require('../../models/voice')

import { TTS_CORE_VERSION } from '../../constants'
import logger from '../../utils/logger'

global.logger = logger
;(async () => {
  await initMongoDB()

  logger.info(`Starting update version emphasis voice ...`, {
    ctx: 'UpdateEmphasisVoiceVersion',
  })

  await Voice.updateMany(
    {
      version: TTS_CORE_VERSION.NEW,
    },
    {
      $set: {
        version: TTS_CORE_VERSION.OLD,
      },
    },
  )

  logger.info(`Update version emphasis voice successfully`, {
    ctx: 'UpdateEmphasisVoiceVersion',
  })
  process.exit(1)
})()
