import {
  KAFKA_TOPIC,
  REDIS_KEY_TTL,
  TTS_CORE_VERSION,
  REQUEST_TYPE,
} from '../constants';
import Caching from './caching';

const md5 = require('md5');
const { updateCachePhrasesInRedis } = require('../daos/tts');
const { sendMessage } = require('./kafka/producer');

const getCacheTTSCachingKey = ({
  template,
  tags,
  voiceCode,
  speedRate,
  bitrate,
  sampleRate,
  backgroundMusic,
}) =>
  md5(
    `${template}${tags}${voiceCode}${speedRate}${bitrate}${sampleRate}${JSON.stringify(
      backgroundMusic,
    )}`,
  );

// Check cache for tts caching
const checkCacheTTSCaching = async ({
  requestId,
  template,
  tags,
  voiceCode,
  speedRate,
  bitrate,
  sampleRate,
  backgroundMusic,
}) => {
  const cacheTTSCachingKey = getCacheTTSCachingKey({
    template,
    tags,
    voiceCode,
    speedRate,
    bitrate,
    sampleRate,
    backgroundMusic,
  });

  const cacheAudioString = await Caching.RedisRepo.get(cacheTTSCachingKey);

  if (cacheAudioString) {
    const synthesisSuccessMessage = {
      value: {
        requestId,
        statusCode: 200,
        audioLink: cacheAudioString,
        totalTime: 0,
        speedRate,
        voiceCode,
        message: 'Caching',
        getSynthesisRequestAt: Date.now(),
        startInvokeLambdaFunctionAt: Date.now(),
        endInvokeLambdaFunctionAt: Date.now(),
        isFromCache: true,
      },
    };
    sendMessage(KAFKA_TOPIC.CACHING_SYNTHESIS_SUCCESS, synthesisSuccessMessage);
    return true;
  }

  return false;
};

// Cache audio link for tts caching
const cacheTTSCachingAudioLink = async ({
  template,
  tags,
  voiceCode,
  speedRate,
  bitrate,
  sampleRate,
  backgroundMusic,
  audioLink,
}) => {
  const cacheSentenceKey = getCacheTTSCachingKey({
    template,
    tags,
    voiceCode,
    speedRate,
    bitrate,
    sampleRate,
    backgroundMusic,
  });

  await Caching.RedisRepo.set(
    cacheSentenceKey,
    audioLink,
    REDIS_KEY_TTL.CACHE_AUDIO_TTS_CACHING,
  );
};
const getCacheSentenceKey = (
  text,
  {
    voiceCode,
    audioType,
    speed,
    bitrate,
    sampleRate,
    dictionary,
    clientPause,
    awsZoneSynthesis,
  },
) =>
  md5(
    `${text}${voiceCode}${audioType}${speed}${bitrate}${sampleRate}${dictionary}${clientPause}${awsZoneSynthesis}`,
  );

const getSynthesisSentences = async ({
  sentences = [],
  request,
  dictionary,
  version,
  clientPause,
  awsZoneSynthesis,
}) => {
  const {
    _id: requestId,
    voice,
    audioType,
    speed,
    bitrate,
    sampleRate,
    type,
  } = request;

  if (type === REQUEST_TYPE.DUBBING) return sentences;
  const noCacheSentences = await sentences.reduce(async (accPromise, curr) => {
    const acc = await accPromise;
    const { text, subIndex, index, _id } = curr;
    const synthesisObject = { text, subIndex, index, _id };

    if (version !== TTS_CORE_VERSION.NEW) return [...acc, synthesisObject];

    const usedDictionary = dictionary?.words?.some((dic) =>
      text.includes(dic.word),
    );
    if (usedDictionary) return [...acc, synthesisObject];

    const cacheSentenceKey = await getCacheSentenceKey(text, {
      voiceCode: voice.code,
      audioType,
      speed,
      bitrate,
      sampleRate,
      dictionary,
      clientPause,
      awsZoneSynthesis,
    });
    const cacheAudioString = await Caching.RedisRepo.get(cacheSentenceKey);
    if (!cacheAudioString) return [...acc, synthesisObject];

    const cacheAudios = JSON.parse(cacheAudioString);
    const phrases = cacheAudios.map((audioLink, phraseIndex) => ({
      index: phraseIndex,
      audioLink,
      a2aDuration: 0,
      cache: true,
    }));

    updateCachePhrasesInRedis({
      requestId,
      ttsId: _id,
      index,
      subIndex,
      phrases,
    });

    const synthesisSuccessMessage = {
      value: {
        requestId,
        index,
        subIndex,
        ttsId: _id,
        text,
        voiceCode: voice.code,
        phrases,
        t2aDuration: 0,
        synthesisDuration: 0,
        cache: true,
      },
    };
    sendMessage(KAFKA_TOPIC.SYNTHESIS_SUCCESS, synthesisSuccessMessage);

    return acc;
  }, Promise.resolve([]));

  return noCacheSentences;
};

const handleCacheSentence = async ({
  text,
  phrases,
  voiceCode,
  audioType,
  speed,
  bitrate,
  sampleRate,
  dictionary,
  clientPause,
  awsZoneSynthesis,
}) => {
  // const dictionary = await findDictionary(userId);
  // const usedDictionary = dictionary?.word?.some((dic) =>
  //   text.includes(dic.word),
  // );
  // if (usedDictionary) return;
  let audios = phrases.reduce((acc, curr) => [...acc, curr.audioLink], []);

  audios = JSON.stringify(audios);

  const cacheSentenceKey = getCacheSentenceKey(text, {
    voiceCode,
    audioType,
    speed,
    bitrate,
    sampleRate,
    dictionary,
    clientPause,
    awsZoneSynthesis,
  });

  await Caching.RedisRepo.set(
    cacheSentenceKey,
    audios,
    REDIS_KEY_TTL.CACHE_AUDIO,
  );
};

module.exports = {
  cacheTTSCachingAudioLink,
  checkCacheTTSCaching,
  getSynthesisSentences,
  handleCacheSentence,
};
