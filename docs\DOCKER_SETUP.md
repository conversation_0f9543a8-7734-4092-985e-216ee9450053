# Docker Setup Guide

## Overview

This guide explains how to build and run the TTS API TypeScript application using Docker. The setup includes both production and development configurations with multi-stage builds for optimal image size and security.

## Prerequisites

1. **Docker** (version 20.10 or higher)
2. **Docker Compose** (version 2.0 or higher)
3. **NPM Token** for accessing private @vbee-holding packages

## Quick Start

### 1. Setup NPM Token

Create a `.npm_token` file with your GitHub NPM token:

```bash
# Linux/macOS/GitBash
echo 'your_github_token_here' > .npm_token

```

### 2. Build and Run

**Using build scripts (recommended):**

```bash
# Linux/macOS
chmod +x docker-build.sh
./docker-build.sh run
```

**Using Docker Compose directly:**

```bash

# Development
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build
```

## Docker Files Structure

- **`docker-build.sh`** - Linux/macOS build script

### Production Files

- **`Dockerfile`** - Multi-stage production build
- **`docker-compose.yml`** - Production services configuration
- **`.dockerignore`** - Files to exclude from build context

### Development Files

- **`Dockerfile.dev`** - Development build with hot reload
- **`docker-compose.dev.yml`** - Development overrides

## Build Process

### Production Build

The production Dockerfile uses a multi-stage build:

1. **Builder Stage:**

   - Installs all dependencies (including devDependencies)
   - Copies TypeScript source code
   - Compiles TypeScript to JavaScript
   - Generates source maps

2. **Production Stage:**
   - Installs only production dependencies
   - Copies compiled JavaScript from builder stage
   - TODO: Creates non-root user for security
   - TODO: Sets up health checks

### Development Build

The development setup provides:

- Hot reload with `ts-node-dev`
- Volume mounting for source code changes
- Debug port exposure (9229)
- Full TypeScript development environment
- Creates non-root user for security
- Sets up health checks

## Available Commands

### Build Scripts

```bash
# Build production image
./docker-build.sh build

# Build development image
./docker-build.sh build dev

# Start production environment
./docker-build.sh run

# Start development environment
./docker-build.sh run dev

# Stop all containers
./docker-build.sh stop

# Clean up resources
./docker-build.sh clean

# View logs
./docker-build.sh logs

# Open shell in container
./docker-build.sh shell

# Run tests
./docker-build.sh test
```

### Direct Docker Commands

```bash
# Build production image
docker build --secret id=npm_token,src=.npm_token -t tts-api:latest .

# Build development image
docker build -f Dockerfile.dev --secret id=npm_token,src=.npm_token -t tts-api:dev .

# Run production stack
docker-compose up -d

# Run development stack
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# View logs
docker-compose logs -f tts-api

# Stop services
docker-compose down
```

## Services Included

The Docker Compose setup includes:

- **tts-api** - Main TypeScript application
- **mongo** - MongoDB database
- **redis** - Redis cache
- **kafka** - Apache Kafka message broker
- **zookeeper** - Kafka dependency

## Health Checks

The application includes health checks:

- **Endpoint:** `http://localhost:8010/health`
- **Interval:** 30 seconds
- **Timeout:** 10 seconds
- **Retries:** 3

## Security Features

- Non-root user execution
- Secret management for NPM tokens
- Environment variable configuration
- Network isolation
- Minimal production image

## Troubleshooting

### Common Issues

1. **NPM Token Error:**

   ```
   Error: npm token file (.npm_token) not found!
   ```

   **Solution:** Create `.npm_token` file with valid GitHub token

2. **Build Fails with TypeScript Errors:**

   - The build continues despite TypeScript errors
   - JavaScript files are still generated
   - Check logs for specific error details

3. **Port Already in Use:**

   ```
   Error: Port 8010 is already in use
   ```

   **Solution:** Stop existing services or change port in docker-compose.yml

4. **Permission Denied:**
   ```
   Error: Permission denied
   ```
   **Solution:** Make build script executable: `chmod +x docker-build.sh`

### Debugging

```bash
# Check container status
docker-compose ps

# View detailed logs
docker-compose logs -f tts-api

# Execute commands in running container
docker-compose exec tts-api sh

# Check TypeScript compilation
docker-compose exec tts-api npm run build
```

## Performance Optimization

- Multi-stage builds reduce final image size
- `.dockerignore` excludes unnecessary files
- Production dependencies only in final stage
- Alpine Linux base for smaller footprint
- Layer caching optimization

## Development Workflow

1. Make changes to TypeScript source files
2. Development container automatically reloads
3. Debug using port 9229
4. Test changes in isolated environment
5. Build production image when ready

This Docker setup provides a complete containerized environment for both development and production deployment of the TTS API TypeScript application.
