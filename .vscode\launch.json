{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug TypeScript",
      "program": "${workspaceFolder}/src/index.ts",
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development",
        "TS_NODE_PROJECT": "${workspaceFolder}/tsconfig.dev.json"
      },
      "console": "integratedTerminal",
      "restart": true,
      "protocol": "inspector",
      "sourceMaps": true,
      "cwd": "${workspaceFolder}",
      "skipFiles": ["<node_internals>/**"],
      "preLaunchTask": "npm: build"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Compiled JS",
      "program": "${workspaceFolder}/dist/index.js",
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "restart": true,
      "protocol": "inspector",
      "sourceMaps": true,
      "cwd": "${workspaceFolder}",
      "skipFiles": ["<node_internals>/**"],
      "preLaunchTask": "npm: build"
    },
    {
      "type": "node-terminal",
      "name": "Run Script: dev",
      "request": "launch",
      "command": "npm run dev",
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "node-terminal",
      "name": "Run Script: start",
      "request": "launch",
      "command": "npm run start",
      "cwd": "${workspaceFolder}"
    }
  ]
}
