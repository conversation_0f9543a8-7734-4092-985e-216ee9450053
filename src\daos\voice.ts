const { findLanguages } = require('./language');
const Voice = require('../models/voice');
const daoUtils = require('./utils');

const createVoice = async (voice) => {
  const newVoice = await Voice.create(voice);

  return newVoice;
};

const createVoices = async (voices) => {
  const voiceIds = voices.map((voice) => voice.code);
  const existingVoices = await Voice.find({ code: { $in: voiceIds } });
  const existingVoiceCodes = existingVoices.map((voice) => voice.code);
  if (existingVoices.length > 0) {
    throw new Error(`Voice already exists:\n${existingVoiceCodes}`);
  }

  const newVoices = await Voice.insertMany(voices, { lean: true });

  return newVoices;
};

/** decorate Voice DTO with language (contain Language DTO) */
const decorateVoicesWithLanguage = async (voices) => {
  if (voices.length === 0) return voices;

  // ADVISE: fetch language from cache
  if (!global.LANGUAGES) {
    const { languages } = await findLanguages();
    global.LANGUAGES = languages;
  }

  return voices.map((voice) => {
    const language = global.LANGUAGES.find(
      (item) => item.code === voice.languageCode,
    );
    return { ...voice, language };
  });
};

/**
 *
 * @param {*} query
 * @returns Array of Voice DTO
 */
const findVoices = async (query = {}) => {
  const {
    search,
    searchFields = ['name'],
    query: queryField,
    offset,
    limit,
    fields,
    sort = ['rank_asc'],
  } = query;

  let dataQuery = {};
  if (queryField) {
    const { gender, languageCode, features, level, ...otherQuery } = queryField;
    dataQuery = { ...otherQuery };
    if (gender) dataQuery.gender = { $in: gender.split(',') };
    if (languageCode) dataQuery.languageCode = { $in: languageCode.split(',') };
    if (level) dataQuery.level = { $in: level.split(',') };
    if (features)
      dataQuery.features = { $elemMatch: { $in: features.split(',') } };
  }

  const { documents: voices, total } = await daoUtils.find(Voice, {
    search,
    searchFields,
    query: dataQuery,
    offset,
    limit,
    fields,
    sort,
  });

  return { voices: await decorateVoicesWithLanguage(voices), total };
};

const updateVoice = async (voiceId, updateFields) => {
  const voice = await Voice.findByIdAndUpdate(voiceId, updateFields, {
    new: true,
    omitUndefined: true,
  }).lean();

  return voice;
};

const findVoiceByCode = async (code) => {
  const voice = await Voice.findOne({ code }).lean();
  return voice;
};

module.exports = {
  createVoice,
  createVoices,
  findVoices,
  updateVoice,
  findVoiceByCode,
};
