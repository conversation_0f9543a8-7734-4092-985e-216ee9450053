import { REQUEST_TYPE } from '../../constants';
const { getAwsZones } = require('../../daos/awsZone');
import logger from '../../utils/logger';

const createArrayFollowWeight = (awsZones) => {
  const arrayFollowWeight = [];
  awsZones.forEach(({ region, weight }) => {
    for (let i = 0; i < weight; i += 1) {
      arrayFollowWeight.push(region);
    }
  });
  return arrayFollowWeight;
};

const initTtsCachingAwsZone = async () => {
  const awsZones = (await getAwsZones()) || [];
  const filteredZones = awsZones.filter(({ allowRequestTypes }) =>
    allowRequestTypes.includes(REQUEST_TYPE.API_CACHING),
  );
  const arrayFollowWeight = createArrayFollowWeight(filteredZones);
  global.AWS_ZONES_TTS_CACHING = arrayFollowWeight;

  logger.info('TTSCaching: Init successfully', {
    ctx: 'AwsZone.TTSCaching',
  });
};

const initTtsStudioAwsZone = async () => {
  const awsZones = (await getAwsZones()) || [];
  const filteredZones = awsZones.filter(({ allowRequestTypes }) => allowRequestTypes.includes(REQUEST_TYPE.STUDIO));
  const arrayFollowWeight = createArrayFollowWeight(filteredZones);
  global.AWS_ZONES_TTS_STUDIO = arrayFollowWeight;

  logger.info('TTSStudio: Init successfully', {
    ctx: 'AwsZone.TTSStudio',
  });
};

module.exports = { initTtsCachingAwsZone, initTtsStudioAwsZone };
