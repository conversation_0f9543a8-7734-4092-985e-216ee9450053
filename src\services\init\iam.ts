const callApi = require('../../utils/callApi');
import configs from '../../configs';
import logger from '../../utils/logger';

const initPublicKey = async () => {
  let { publicKey } = await callApi({
    method: 'GET',
    url: `${configs.IAM_URL}/auth/realms/${configs.IAM_REALM}`,
  });

  publicKey = `-----BEGIN PUBLIC KEY-----\r\n${publicKey}\r\n-----END PUBLIC KEY-----`;
  global.IAM_PUBLIC_KEY = publicKey;

  logger.info('PublicKey: Init successfully', {
    ctx: 'IAM.PublicKey',
  });
};

const initIAM = async () => {
  await Promise.all([
    initPublicKey(),
    // scheduleTokenRefresh()
  ]);
};

module.exports = { initIAM };
