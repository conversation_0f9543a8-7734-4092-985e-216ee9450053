import { initRoute } from './routes'
import logger from './utils/logger'
import configs from './configs'

export const initHttpServer = async () =>
  new Promise((resolve, reject) => {
    try {
      const { PORT } = configs

      const http = require('http')
      const express = require('express')

      const cors = require('cors')
      const helmet = require('helmet')
      const compression = require('compression')

      // static middlewares (no depend on external services)
      const camelCaseReq = require('./middlewares/camelCaseReq')
      const omitReq = require('./middlewares/omitReq')
      const snakeCaseRes = require('./middlewares/snakeCaseRes')
      const successReq = require('./middlewares/successReq')
      const getClientInfo = require('./middlewares/getClientInfo')

      // middlewares depend on external services (DB, Cache, metric ...)
      const errorHandler = require('./middlewares/errorHandler')

      const app = express()

      // Global (static) Middlewares setup
      app.use(cors())
      app.use(helmet())
      app.use(compression())
      app.use(express.json({ limit: '10MB' }))
      app.use(express.urlencoded({ extended: true }))
      app.use(camelCaseReq)
      app.use(omitReq)
      app.use(snakeCaseRes())
      app.use(successReq())
      app.use(getClientInfo)

      // Route registration
      initRoute(app)

      app.use(errorHandler)

      const server = http.createServer(app)
      server.listen(PORT, () => {
        logger.info(`HttpServer: Init successfully, running on port ${PORT}`, {
          ctx: 'Server.Http',
        })

        resolve(server)
      })
    } catch (error) {
      reject(error)
    }
  })

module.exports = { initHttpServer }
