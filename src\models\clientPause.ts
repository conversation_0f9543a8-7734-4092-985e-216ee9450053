const mongoose = require('mongoose');
import { VALID_CLIENT_PAUSE } from '../constants/clientPause';

const ClientPauseSchema = new mongoose.Schema(
  {
    userId: String,
    // \n
    paragraphBreak: {
      type: Number,
      min: VALID_CLIENT_PAUSE.MIN,
      max: VALID_CLIENT_PAUSE.MAX,
    },
    // .
    sentenceBreak: {
      type: Number,
      min: VALID_CLIENT_PAUSE.MIN,
      max: VALID_CLIENT_PAUSE.MAX,
    },
    // ;
    majorBreak: {
      type: Number,
      min: VALID_CLIENT_PAUSE.MIN,
      max: VALID_CLIENT_PAUSE.MAX,
    },
    // ,
    mediumBreak: {
      type: Number,
      min: VALID_CLIENT_PAUSE.MIN,
      max: VALID_CLIENT_PAUSE.MAX,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

module.exports = mongoose.model('ClientPause', ClientPauseSchema);
