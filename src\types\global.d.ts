// Global type declarations for the TTS API project

declare global {
  var logger: any;
  var IAM_PUBLIC_KEY: any;
  var VOICES: any;
  var LANGUAGES: any;
  var KAFKA_CONSUMER_GROUP_RANDOM_TTS: string;
  var REQUEST_DIRECT: any;
  var AWS_ZONES_TTS_CACHING: any;
  var AWS_ZONES_TTS_STUDIO: any;
  var APPS: any;
}

// Module declarations for CommonJS modules without types
declare module '*' {
  const content: any;
  export = content;
}

export {};
