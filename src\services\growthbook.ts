import configs from '../configs'
const { GROWTH_BOOK_API_HOST, GROWTH_BOOK_CLIENT_KEY, LOADING_FEATURES_REALTIME_INTERVAL } = configs
import logger from '../utils/logger'

import { GrowthBookAdapter } from '@vbee-holding/vbee-tts-node-datastore'

let GrowthBookSingleton: GrowthBookAdapter

const init = async () => {
  GrowthBookSingleton = new GrowthBookAdapter({
    apiHost: GROWTH_BOOK_API_HOST,
    clientKey: GROWTH_BOOK_CLIENT_KEY,
    logger,
  })

  await GrowthBookSingleton.init()
  logger.info('GrowthBook: create client and load features for the first time', {
    ctx: 'GrowthBook',
  })

  await GrowthBookSingleton.scheduleRefreshFeatures(LOADING_FEATURES_REALTIME_INTERVAL)
  logger.info('GrowthBook: kick off the scheduler to refresh features using Polling Approach', {
    ctx: 'GrowthBook',
  })

  logger.info('GrowthBook: Init successfully,', {
    ctx: 'GrowthBook',
    apiHost: GROWTH_BOOK_API_HOST,
  })
}

/** get feature value from the RemoteConfigRepository */
export const getFeatureValue = GrowthBookSingleton.getValue

export default {
  GrowthBookSingleton,
  init,
}
