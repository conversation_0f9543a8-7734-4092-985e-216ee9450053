import { RESPONSE_TYPE, OUTPUT_TYPE } from '../constants'
// ADVISE: change to mapVoiceCodes, move to shared-models
import mapVoices from '../constants/mapVoices.json'

const apiSynthesisService = require('../services/apiSynthesis')

const appDao = require('../daos/app')
const errorCodes = require('../errors/code')
const CustomError = require('../errors/CustomError')
import { getFeatureValue } from '../services/growthbook'

import FEATURE_KEYS from '../constants/featureKeys'
import logger from '../utils/logger'

const apiSynthesis = async (req, res) => {
  const { apiApp: app } = req
  const { publicIP: ip } = req.__clientInfo

  console.warn('----------------------------')
  console.warn('apiSynthesis, accept request from vbee-tts-api')
  console.warn('----------------------------')

  const {
    title,
    responseType,
    callbackUrl,
    callbackUpdateProgressUrl,
    inputText,
    voiceCode,
    audioDomainType,
    outputType,
    sentences,
    audioType,
    bitrate,
    sampleRate,
    speedRate,
    backgroundMusic,
    fromVn,
    clientUserId,
    retentionPeriod,
    dictionary,
    clientPause,
    sessionId,
    synthesisCcr,
    returnTimestamp,
    synthesisComputePlatform,
    isPriority = false,
    isRealTime = false,
  } = req.body

  const normalizedSentences = apiSynthesisService.standardizeSentences(
    sentences,
    synthesisComputePlatform /** "CLOUD_RUN" */,
  )
  const request = await apiSynthesisService.handleApiSynthesisRequest({
    ip,
    app,
    title,
    responseType,
    callbackUrl,
    callbackUpdateProgressUrl,
    text: inputText,
    voiceCode,
    outputType,
    audioDomainType,
    sentences: normalizedSentences,
    audioType,
    bitrate,
    sampleRate,
    speed: speedRate,
    backgroundMusic,
    fromVn,
    clientUserId,
    retentionPeriod,
    dictionary,
    clientPause,
    sessionId,
    synthesisCcr,
    returnTimestamp,
    synthesisComputePlatform,
    isPriority,
    isRealTime,
  })

  if (responseType === RESPONSE_TYPE.DIRECT) {
    global.REQUEST_DIRECT[request.requestId] = res
    logger.info(Object.keys(global.REQUEST_DIRECT), { ctx: 'ApiSynthesis' })
    return null
  }

  return res.send(request)
}
const apiSynthesisMobifone = async (req, res) => {
  const { apiApp: app } = req
  const { publicIP: ip } = req.__clientInfo
  logger.info(`Synthesize api request from ${ip}`)

  const {
    voice,
    inputText,
    content,
    audioType,
    rate,
    bitRate,
    urlCallbackApi,
    dictionary,
    httpCallback,
    clientUserId,
    requestId: sessionId,
    responseType = RESPONSE_TYPE.DIRECT,
  } = req.body
  const voiceCode = mapVoices[voice] || voice

  const hasEnableBlockUnsupportedVoice = getFeatureValue(FEATURE_KEYS.BLOCK_UNSUPPORTED_VOICE)
  if (hasEnableBlockUnsupportedVoice) {
    const { appId } = req.body
    const availableVoiceCodes = await appDao.getAvailableVoiceCodes(appId)
    const isValidVoice = availableVoiceCodes.includes(voiceCode)
    if (!isValidVoice) throw new CustomError(errorCodes.BAD_REQUEST, 'Invalid voice')
  }

  const request = await apiSynthesisService.handleApiSynthesisRequest({
    ip,
    app,
    text: inputText || content,
    callbackUrl: urlCallbackApi || httpCallback,
    responseType,
    voiceCode,
    audioType,
    outputType: OUTPUT_TYPE.LINK,
    bitrate: bitRate,
    speed: rate,
    sessionId,
    dictionary,
    clientUserId,
  })

  if (responseType === RESPONSE_TYPE.DIRECT) {
    global.REQUEST_DIRECT[request.requestId] = res
    return null
  }

  return res.send(request)
}

module.exports = { apiSynthesis, apiSynthesisMobifone }
