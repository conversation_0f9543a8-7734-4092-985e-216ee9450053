const { Joi, validate } = require('express-validation');
import {
  AUDIO_TYPE,
  RESPONSE_TYPE,
  VALID_SPEED,
  VALID_BIT_RATE,
  VALID_BACKGROUND_MUSIC_VOLUME,
  TTS_CORE_COMPUTE_PLATFORM,
} from '../constants';

const sentence = Joi.object().keys({
  inputText: Joi.string().required(),
  voiceCode: Joi.string().required(),
  speedRate: Joi.number().min(VALID_SPEED.MIN).max(VALID_SPEED.MAX).optional(),
  breakTime: Joi.number().optional(),
  synthesisComputePlatform: Joi.string()
    .valid(...Object.values(TTS_CORE_COMPUTE_PLATFORM))
    .optional(),
});

const elemDictionary = Joi.object().keys({
  word: Joi.string().required(),
  pronunciation: Joi.string().required(),
});

const synthesisApi = {
  body: Joi.object({
    appId: Joi.string().trim().required(),
    responseType: Joi.string()
      .valid(...Object.values(RESPONSE_TYPE))
      .optional(),
    audioDomainType: Joi.string().optional(),
    callbackUrl: Joi.string()
      .uri()
      .trim()
      .when('responseType', {
        is: Joi.string().valid(RESPONSE_TYPE.INDIRECT),
        then: Joi.required(),
        otherwise: Joi.optional(),
      }),
    callbackUpdateProgressUrl: Joi.string().uri().trim().optional(),
    title: Joi.string().optional().allow(''),
    synthesisCcr: Joi.number().optional(),
    inputText: Joi.string().trim().when('sentences', {
      not: Joi.exist(),
      then: Joi.required(),
      otherwise: Joi.optional(),
    }),
    voiceCode: Joi.string().when('inputText', {
      is: Joi.exist(),
      then: Joi.required(),
      otherwise: Joi.optional(),
    }),
    sentences: Joi.array().min(1).items(sentence).optional(),
    audioType: Joi.string()
      .valid(...Object.values(AUDIO_TYPE))
      .optional(),
    bitrate: Joi.number()
      .valid(...VALID_BIT_RATE)
      .optional(),
    sampleRate: Joi.number().optional(),
    speedRate: Joi.number().min(VALID_SPEED.MIN).max(VALID_SPEED.MAX).optional(),
    fromVn: Joi.boolean().optional(),
    backgroundMusic: Joi.object({
      link: Joi.string().trim().optional(),
      volume: Joi.number()
        .min(VALID_BACKGROUND_MUSIC_VOLUME.MIN)
        .max(VALID_BACKGROUND_MUSIC_VOLUME.MAX)
        .default(VALID_BACKGROUND_MUSIC_VOLUME.DEFAULT)
        .optional(),
    }).optional(),
    retentionPeriod: Joi.number().optional(),
    dictionary: Joi.array().items(elemDictionary).optional(),
    clientPause: Joi.object({
      paragraphBreak: Joi.number().optional(),
      sentenceBreak: Joi.number().optional(),
      majorBreak: Joi.number().optional(),
      mediumBreak: Joi.number().optional(),
    }).optional(),
    clientUserId: Joi.string().trim().optional(),
    sessionId: Joi.string().optional(),
    returnTimestamp: Joi.boolean().optional(),
    synthesisComputePlatform: Joi.string()
      .valid(...Object.values(TTS_CORE_COMPUTE_PLATFORM))
      .optional(),
    isPriority: Joi.boolean().optional(),
    isRealTime: Joi.boolean().optional(),
  }).without('inputText', 'sentences'),
};

module.exports = {
  synthesisApiValidate: validate(synthesisApi, { keyByField: true }),
};
