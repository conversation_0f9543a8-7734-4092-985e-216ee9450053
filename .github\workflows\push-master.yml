name: Push master

on:
  push:
    branches: [master]

jobs:
  dev-ci:
    uses: vbee-holding/shared-github-actions/.github/workflows/build-push-image.yml@v1
    with:
      registry_type: 'GAR'
      gar_location: asia-southeast1
      repository: ${{ github.event.repository.name }}
      tag: ${{ github.run_id }}
    secrets:
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      GAR_CREDENTIALS: ${{ secrets.VBEE_DEV_GAR_PUSH_KEY }}
