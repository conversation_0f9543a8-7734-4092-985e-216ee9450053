import { <PERSON><PERSON><PERSON><PERSON> } from '@vbee-holding/vbee-node-shared-lib';
import configs from '../configs';
const {
  MULTI_ZONE,
  DEFAULT_AWS_REGION,
  STUDIO_TTS_VERSION_DEFAULT,
  IS_TTS_GATE,
} = configs;
const { RandomFactory } = require('../utils/random');
const { readFileFromLink } = require('../utils/file');
const { validateText, getSubtitleContent } = require('./preprocessing');
const { parseSRT, getSRTDuration } = require('../utils/srt');
const CustomError = require('../errors/CustomError');
const code = require('../errors/code');
const { getVoiceByCode } = require('./voice');
const { getAwsZone } = require('../daos/awsZone');
import {
  DUBBING_AUDIO_DURATION_LIMIT,
  DUBBING_MAX_SENTENCE_LENGTH,
} from '../constants/dubbing';
import {
  REQUEST_STATUS,
  REQUEST_TYPE,
  SERVICE_TYPE,
  DEFAULT_RETENTION_PERIOD,
  RESPONSE_TYPE,
  REDIS_KEY_PREFIX,
  TTS_CORE_COMPUTE_PLATFORM,
} from '../constants';
const { createRequest, createRequestInRedis } = require('./request');
const { sendPendingRequestToKafka } = require('./queue');
const { getTtsFromRequestIdInRedis } = require('../daos/tts');
const { executeCallApiSynthesizeSubtitle } = require('./ttsCore');
const { detectCloudStorageService } = require('./audio');

const validateVoiceForDubbing = (voice) => {
  if (!voice.hasDubbing) throw new CustomError(code.VOICE_NOT_SUPPORT_DUBBING);
};

const validateTextForDubbing = (plainText, voice) => {
  const { provider, version } = voice;
  const isValidText = validateText({
    text: plainText,
    voiceProvider: provider,
    ttsCoreVersion: version || STUDIO_TTS_VERSION_DEFAULT,
  });

  if (!isValidText) {
    throw new CustomError(code.INVALID_SYNTAX);
  }
};

const validateMultipleVoice = async (voiceCodes) => {
  const voices = await Promise.all(
    voiceCodes.map(async (voiceCode) => {
      const voice = await getVoiceByCode(voiceCode);
      validateVoiceForDubbing(voice);
      return voice;
    }),
  );

  return voices;
};

// ADVISE: duplicate code
const getAwsZoneSynthesis = () => {
  if (!MULTI_ZONE) return DEFAULT_AWS_REGION;

  const lengthAwsZones = AWS_ZONES_TTS_STUDIO.length;
  const randomIndex = Math.floor(Math.random() * lengthAwsZones);
  const awsZone = AWS_ZONES_TTS_STUDIO[randomIndex];

  return awsZone || DEFAULT_AWS_REGION;
};

const handleDubbing = async ({
  ip,
  app,
  responseType = RESPONSE_TYPE.INDIRECT,
  outputType,
  v3ApiType,
  callbackUrl,
  callbackUpdateProgressUrl,
  title,
  createdAt = new Date(),
  speedRate = 1,
  bitrate,
  voiceCode,
  audioType = 'wav',
  subtitleLink,
  sampleRate,
  serviceType,
  clientUserId,
  retentionPeriod = DEFAULT_RETENTION_PERIOD,
  sessionId,
  synthesisCcr,
  sentencesVoiceCode = {},
  synthesisComputePlatform = TTS_CORE_COMPUTE_PLATFORM.LAMBDA,
}) => {
  const requestId = RandomFactory.getGuid();

  const voiceCodes = Object.keys(sentencesVoiceCode);
  const isMultipleVoices = voiceCodes.length > 0;

  // Get subtitle content
  const fileContent = await readFileFromLink(subtitleLink);
  const subtitlesContent = getSubtitleContent(fileContent);
  const plainText = subtitlesContent.join(' ');

  // Validate srt format
  const { error, message, parsedSubtitles } = parseSRT(fileContent);
  if (error) throw new CustomError(code.INVALID_SRT_FORMAT, message);

  // Validate audio duration
  const subtitleDuration = getSRTDuration(parsedSubtitles);
  if (subtitleDuration > DUBBING_AUDIO_DURATION_LIMIT * 3600 * 1000) {
    throw new CustomError(code.DUBBING_AUDIO_TOO_LONG);
  }

  // Validate subtitle language
  const isVietnameseSubtitle = await VietnameseHelper.isVietnameseText(
    plainText,
  );
  if (!isVietnameseSubtitle)
    throw new CustomError(code.DUBBING_ONLY_SUPPORT_VIETNAMESE);

  // Validate sentence length
  const sentenceTooLong = parsedSubtitles.some(
    (subtitle) => subtitle.content?.length > DUBBING_MAX_SENTENCE_LENGTH,
  );
  if (sentenceTooLong) throw new CustomError(code.DUBBING_SENTENCE_TOO_LONG);

  const characters = subtitlesContent.join('').length;

  // Validate voice and text
  let voice;

  if (isMultipleVoices) {
    const voices = await validateMultipleVoice(voiceCodes);

    voices.forEach((voiceInfo) => {
      validateTextForDubbing(plainText, voiceInfo);
    });
    [voice] = voices;
  } else {
    voice = await getVoiceByCode(voiceCode);
    validateVoiceForDubbing(voice);
    validateTextForDubbing(plainText, voice);
  }

  const awsZoneSynthesis = getAwsZoneSynthesis();
  const awsZone = (await getAwsZone({ region: awsZoneSynthesis })) || {};

  const {
    normalizerFunction,
    sentenceTokenizerFunction,
    newSentenceTokenizerFunction,
    textToAllophoneFunction,
    synthesisFunction,
    joinSentencesFunction,
    srtFunction,
    defaultS3Bucket,
    s3Buckets = {},
  } = awsZone;

  // Save request to database
  const request = {
    ip,
    requestId,
    title,
    text: fileContent,
    characters,
    subtitleLink,
    audioType,
    speed: speedRate,
    createdAt,
    status: REQUEST_STATUS.IN_PROGRESS,
    voiceCode,
    bitrate,
    sampleRate: sampleRate
      ? sampleRate.toString()
      : voice.defaultSampleRate.toString(),
    retentionPeriod,
    app: app._id,
    callbackUrl,
    callbackUpdateProgressUrl,
    responseType,
    outputType,
    v3ApiType,
    type: REQUEST_TYPE.DUBBING,
    version: voice.version || STUDIO_TTS_VERSION_DEFAULT,
    serviceType: serviceType || SERVICE_TYPE.AI_VOICE,
    clientUserId,
    awsZoneSynthesis,
    awsZoneFunctions: {
      normalizerFunction,
      sentenceTokenizerFunction,
      newSentenceTokenizerFunction,
      textToAllophoneFunction,
      synthesisFunction,
      joinSentencesFunction,
      srtFunction,
      s3Bucket: s3Buckets[retentionPeriod] || defaultS3Bucket,
    },
    sessionId,
    synthesisCcr,
    synthesisComputePlatform,
  };
  if (isMultipleVoices) request.sentencesVoiceCode = sentencesVoiceCode;

  await createRequest(request);

  const cacheRequest = {
    ...request,
    numberOfSentences: 0,
    voice,
    progress: 0,
    sentenceKeys: [`${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${0}`],
    numberOfIndexSentences: 1,
  };

  await createRequestInRedis(cacheRequest);

  if (IS_TTS_GATE) {
    executeCallApiSynthesizeSubtitle({
      userId: clientUserId,
      requestId,
      title,
      audioType,
      bitrate,
      sampleRate,
      speedRate,
      synthesisCcr,
      subtitleLink,
      sentencesVoiceCode,
      voiceCode,
    });
  } else {
    sendPendingRequestToKafka({ request: cacheRequest, userId: clientUserId });
  }

  delete request.voiceCode;
  delete request.awsZoneFunctions;
  delete request.awsZoneSynthesis;
  return request;
};

const processTtsDubbingToJoinAudios = async (requestId) => {
  const ttsList = await getTtsFromRequestIdInRedis(requestId);

  const subtitles = ttsList.map((tts) => ({
    start: tts.start,
    end: tts.end,
    audio_name: tts.audioName,
    audio_source: detectCloudStorageService(tts?.audioLink),
  }));

  return subtitles;
};

module.exports = { handleDubbing, processTtsDubbingToJoinAudios };
