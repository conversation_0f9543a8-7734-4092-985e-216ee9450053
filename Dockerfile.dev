# Development Dockerfile for hot reloading
FROM node:20.19-alpine
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig*.json ./
COPY .npmrc ./

# Creating .npmrc on-the-fly to access private repo inside vbee-holding org.
# Then install ALL dependencies (including devDependencies for TypeScript compilation)
RUN --mount=type=secret,id=npm_token \
  export NPM_TOKEN=$(cat /run/secrets/npm_token) && \
  npm ci && \
  rm .npmrc

# Copy source code
COPY src/ ./src/

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
  adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port and debug port
EXPOSE 8010 9229

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:8010/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# Start the development server with hot reload
CMD ["npm", "run", "dev"]
