const { default: axios } = require('axios')
const https = require('https')
import {
  KAFKA_TOPIC,
  REQUEST_TYPE,
  RESPONSE_TYPE,
  REDIS_KEY_PREFIX,
  REDIS_KEY_TTL,
  LOADING_SYNTHESIS_FACTOR,
  REQUEST_STATUS,
  VOICE_PROVIDER,
  NON_VBEE_PROVIDERS,
  TTS_CORE_VERSION,
} from '../constants'
import FEATURE_KEYS from '../constants/featureKeys'
import logger from '../utils/logger'

import { getFeatureValue } from './growthbook'
import requestDao from '../daos/request'
const { checkCompletedIndexInRedis } = require('../daos/tts')
const { sendMessage } = require('./kafka/producer')
import Caching from './caching'

const { sendApiResponse } = require('./apiResponse')

const handleTtsFailure = ({ request, errorCode, errorMessage }) => {
  const { _id: requestId, type, responseType } = request
  const { title, text, sentences, paragraphs, ...requestData } = request

  // Send callback for sync request api
  if (responseType === RESPONSE_TYPE.INDIRECT) {
    sendApiResponse({
      request: { requestId, ...requestData },
      errorCode,
      errorMessage,
    })
  }

  const hasPublishRequest =
    (type === REQUEST_TYPE.API || type === REQUEST_TYPE.API_CACHING) && responseType === RESPONSE_TYPE.DIRECT

  if (hasPublishRequest)
    sendMessage(KAFKA_TOPIC.PUBLISH_TTS_FAILURE, {
      value: { errorMessage, errorCode, requestId, ...requestData },
    })
}

const handleTtsSuccess = (request, preSendResponseAt) => {
  const { _id: requestId, type, responseType } = request
  const { title, text, sentences, paragraphs, ...requestData } = request

  // Send callback for sync request api
  const validRequestTypes = [REQUEST_TYPE.API, REQUEST_TYPE.DUBBING, REQUEST_TYPE.API_CACHING]
  const isApiRequest = validRequestTypes.includes(type)
  const isIndirectResponse = responseType === RESPONSE_TYPE.INDIRECT

  if (isApiRequest && isIndirectResponse) {
    sendApiResponse({
      request: { requestId, text, sentences, ...requestData },
    })
  }

  // Publish to kafka for demo and async request
  const hasPublishRequest =
    (type === REQUEST_TYPE.API || type === REQUEST_TYPE.API_CACHING) && responseType === RESPONSE_TYPE.DIRECT
  if (hasPublishRequest)
    sendMessage(KAFKA_TOPIC.PUBLISH_TTS_SUCCESS, {
      value: { requestId, text, sentences, preSendResponseAt, ...requestData },
    })
}

const saveTtsIdsInRequest = async (requestId) => {
  const request = await requestDao.findRequestByIdInRedis(requestId)

  const numberOfIndexSentences = request.numberOfIndexSentences || 1
  const numOfSentenceCompletedKey = `${REDIS_KEY_PREFIX.COMPLETED_SENTENCE_TOKENIZATION}_${requestId}`
  const numOfSentenceCompleted = await Caching.GlobalCounter.increase(numOfSentenceCompletedKey)
  await Caching.RedisRepo.expire(numOfSentenceCompletedKey, REDIS_KEY_TTL.COMPLETED_SENTENCE_TOKENIZATION)

  logger.info({ numberOfIndexSentences, numOfSentenceCompleted }, { ctx: 'RequestRedis' })
  if (Number(numOfSentenceCompleted) === numberOfIndexSentences) {
    const { sentenceKeys = [] } = request
    const sentencesCache = await Caching.RedisRepo.multiGet(sentenceKeys)

    const checkNullElement = sentencesCache.some((sentence) => !sentence)
    if (!checkNullElement) {
      const ttsIds = sentencesCache.reduce((acc, sentence) => [...acc, ...JSON.parse(sentence)], [])
      request.ttsIds = ttsIds
      const requestKey = `${REDIS_KEY_PREFIX.REQUEST}_${requestId}`

      await Caching.RedisRepo.set(requestKey, JSON.stringify(request), REDIS_KEY_TTL.SYNTHESIS_REQUEST)
      const totalTtsRequestKey = `${REDIS_KEY_PREFIX.TOTAL_TTS}_${requestId}`
      const totalTtsRequest = ttsIds.length
      await Caching.RedisRepo.set(totalTtsRequestKey, totalTtsRequest, REDIS_KEY_TTL.SENTENCE)
    }
  }
}

const checkAvailableBreakTime = ({ voice, voiceOverride, userId, ttsCoreVersion, request, index }) => {
  const voiceValue = voiceOverride || voice
  const isNonVbeeBreakTime = getFeatureValue(FEATURE_KEYS.NON_VBEE_BREAK_TIME, {
    voiceProvider: voiceValue.provider,
    userId,
  })

  const isVbeeVoice = voiceValue.provider === VOICE_PROVIDER.VBEE
  const isVbeeVoiceCloning = voiceValue.provider === VOICE_PROVIDER.VBEE_VOICE_CLONING
  const isNonVbeeVoice = NON_VBEE_PROVIDERS.includes(voiceValue.provider)
  const breakTime = request.sentences?.[index]?.breakTime || 0

  const useBreakTime =
    ttsCoreVersion !== TTS_CORE_VERSION.NEW &&
    request.sentences?.length &&
    (isVbeeVoice || isVbeeVoiceCloning || (isNonVbeeBreakTime && isNonVbeeVoice)) &&
    breakTime

  return useBreakTime
}

const checkCompletedSynthesis = async ({ requestId, numberOfSentences, numOfCompletedTts, numOfTts }) => {
  logger.info({ requestId, numOfTts, numOfCompletedTts }, { ctx: 'CheckCompletedSynthesis' })
  if (numOfTts < numberOfSentences) return false
  if (numOfCompletedTts < numOfTts) return false

  const indexes = Array.from(Array(numberOfSentences).keys())
  const checks = await Promise.all(
    indexes.map(async (index) => {
      const check = await checkCompletedIndexInRedis(requestId, index)
      return check
    }),
  )
  return checks.every((check) => check)
}

const sendCallbackRequestProgress = async (url, payload) => {
  try {
    const httpsAgent = new https.Agent({ rejectUnauthorized: false })

    const response = await axios({
      method: 'PUT',
      url,
      data: payload,
      httpsAgent,
    })

    logger.info(JSON.stringify(response.data), {
      ctx: 'SendApiResponse',
    })
  } catch (err) {
    logger.error(err, { ctx: 'SendApiResponse', payload })
  }
}

const handleUpdateProgressTTS = async ({
  requestId,
  userId,
  progress,
  status = REQUEST_STATUS.IN_PROGRESS,
  sessionId,
  audioLink,
  index,
  subIndex,
  phrases,
  error,
  callbackUpdateProgressUrl,
}) => {
  if (callbackUpdateProgressUrl) {
    const request = await requestDao.findRequestByIdInRedis(requestId)
    const tts = request.ttsIds?.reduce(
      (prevTts, ttsItem) => {
        // eslint-disable-next-line no-shadow
        const { index, subIndex } = ttsItem
        const ttsIndexOrder = prevTts.find((order) => order.index === index)
        if (ttsIndexOrder) {
          if (ttsIndexOrder.lastSubIndex < subIndex) ttsIndexOrder.lastSubIndex = subIndex
        } else prevTts.push({ index, lastSubIndex: subIndex })

        return prevTts
      },
      [{ index: 0, lastSubIndex: 0 }],
    )

    const payload = {
      requestId: sessionId,
      userId,
      progress,
      status,
      index,
      subIndex,
      phrases,
      error,
      audioLink,
      tts,
    }

    sendCallbackRequestProgress(callbackUpdateProgressUrl, payload)
  }
}

/** pure function, calculation */
const calProgressWhenSynthesisSuccess = (numOfCompletedTts, numOfTts) => {
  const { SENTENCE_TOKENIZATION, SYNTHESIS_SENTENCE_SUCCESS } = LOADING_SYNTHESIS_FACTOR
  const maxSynthesisProgress = SENTENCE_TOKENIZATION + SYNTHESIS_SENTENCE_SUCCESS
  const normalizedNumOfTts = numOfTts || 1

  const progress =
    SENTENCE_TOKENIZATION * 100 +
    Math.round((numOfCompletedTts / normalizedNumOfTts) * SYNTHESIS_SENTENCE_SUCCESS * 100)

  return progress / 100 > maxSynthesisProgress ? maxSynthesisProgress * 100 : progress
}

const updateProgressWhenSynthesisSuccessInRedis = async ({
  requestId,
  userId,
  status = REQUEST_STATUS.IN_PROGRESS,
  sessionId,
  audioLink,
  index,
  subIndex,
  phrases,
  progress,
  callbackUpdateProgressUrl,
}) => {
  handleUpdateProgressTTS({
    requestId,
    userId,
    progress,
    status,
    sessionId,
    audioLink,
    index,
    subIndex,
    phrases,
    callbackUpdateProgressUrl,
  })
}

module.exports = {
  handleTtsFailure,
  handleTtsSuccess,
  saveTtsIdsInRequest,
  checkCompletedSynthesis,
  handleUpdateProgressTTS,
  calProgressWhenSynthesisSuccess,
  updateProgressWhenSynthesisSuccessInRedis,
  checkAvailableBreakTime,
}
