# TypeScript Setup Guide

## Overview

- This project has been successfully migrated from JavaScript to TypeScript. All `.js` files have been renamed to `.ts` files
- The development environment has been configured to work with TypeScript.
- Repo is configured to be debuggable when pressing F5.
- The current setup allows for normal development while providing a foundation for gradual TypeScript adoption.

## Current Status

✅ **Completed:**

- All `.js` files renamed to `.ts` files (97 files total)
- TypeScript dependencies installed
- Development environment configured with `ts-node-dev`
- VS Code debugging setup with F5 key support
- ESLint configured for TypeScript
- `package.json` scripts updated
- **Automatic build before start and debug** - `npm run build` runs automatically
- Production and development builds working despite type errors (when build with `npm run build`).

- Express setup and Route setup is converted into ESM module and use declarative routes

⚠️ **In Progress:**

- TypeScript compilation has many errors due to CommonJS module conflicts
- #GRADUAL_MIGRATION to proper TypeScript modules recommended
- Enable stricter TypeScript rules gradually

- reenable lint-staged in `package.json`

```
script: {
  "postinstall": "husky install",
}

"lint-staged": {
    "*.{js,ts}": "eslint"
  }
```

## Development Workflow

### Running the Project

1. **Development mode** (with hot reload):

   ```bash
   npm run dev
   ```

2. **Debug mode** (with inspector):

   ```bash
   npm run dev:debug
   ```

3. **VS Code Debugging**:
   - Press `F5` or use "Debug TypeScript" configuration
   - Set breakpoints in `.ts` files
   - Full debugging support with source maps

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run dev:debug` - Start development server with debugging enabled
- `npm run build` - Compile TypeScript to JavaScript (has errors but still generates files) #GRADUAL_MIGRATION
- `npm run start` - **Automatically builds then** runs compiled JavaScript from dist folder

### Automatic Build Feature

- Automatic Build Integration: npm run build now runs automatically before both production start and F5 debugging
- Seamless Development Experience: No manual build steps required
- Error-Tolerant Building: Build continues and generates JavaScript files even with TypeScript errors

🔧 **Build-Before-Run**: The project now automatically runs `npm run build` before:

- `npm run start` (production mode)
- VS Code F5 debugging (debug mode)

This ensures that:

- Latest TypeScript changes are always compiled before execution
- No manual build step required
- Consistent behavior between development and production
- Build errors are shown but don't prevent execution (files are still generated) #GRADUAL_MIGRATION

## Configuration Files

### TypeScript Configurations

1. **`tsconfig.json`** - Main TypeScript configuration for production builds
2. **`tsconfig.dev.json`** - Development-specific configuration with relaxed rules
3. **`src/types/global.d.ts`** - Global type declarations

### Key Features

- **Source Maps**: Enabled for debugging
- **Hot Reload**: Automatic restart on file changes
- **Transpile Only**: Fast compilation during development
- **CommonJS Support**: Maintains compatibility with existing code structure

## Current Limitations

### TypeScript Compilation Errors

The project currently has ~700 TypeScript compilation errors, primarily due to:

1. **Variable Redeclaration**: CommonJS `require()` statements create global conflicts
2. **Missing Type Definitions**: Many variables lack proper TypeScript types
3. **Module System**: Mixed CommonJS/ES modules causing conflicts

### Recommended Next Steps #GRADUAL_MIGRATION

1. **Convert modules**: one by one to proper ES modules
2. **Add Type Definitions**: Add proper TypeScript interfaces and types
3. **Resolve Conflicts**: Fix variable redeclaration issues
4. **Update Imports**: Convert `require()` to `import` statements

## Development Environment

### ESLint Integration

ESLint is configured to work with TypeScript:

- TypeScript-specific rules enabled
- Airbnb base configuration
- Prettier integration
- Support for both `.js` and `.ts` files

## Troubleshooting

### Common Issues

1. **"Cannot redeclare block-scoped variable"**

   - This is expected due to CommonJS structure
   - Development mode works despite these errors
   - Will be resolved during gradual migration

2. **Import/Export Errors**

   - Use `require()` for now (CommonJS compatibility)
   - Gradually migrate to `import/export` statements

3. **Type Errors**
   - Development mode uses `transpileOnly: true`
   - Type checking is relaxed for development
   - Add proper types gradually

# VS Code Launch Configuration:

- Added "preLaunchTask": "npm: build" to debug configurations
- Created .vscode/tasks.json with build task definition

- Multiple debug options available:

  - "Debug TypeScript" - Direct TypeScript debugging with auto-build
  - "Debug Compiled JS" - Debug compiled JavaScript with auto-build

- Build Process Features:
  - Pre-start Hook: prestart script automatically runs build before start
  - Error Tolerance: Uses || echo to continue even with TypeScript errors
  - File Generation: TypeScript compiler generates JavaScript files despite errors (due to "noEmitOnError": false)

## 🚀 How It Works

### For Production (npm run start):

- prestart script triggers automatically
- Runs npm run build which compiles TypeScript
- Shows compilation errors but continues
- Generates JavaScript files in dist/ folder
- Starts the application with node dist/index.js

### For F5 Debugging:

- VS Code triggers the npm: build pre-launch task
- TypeScript compilation runs and shows errors
- JavaScript files are generated with source maps
- Debugger attaches to the TypeScript source files
- Full debugging support with breakpoints
