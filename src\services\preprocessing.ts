const cheerio = require('cheerio');
import { TextHelper } from '@vbee-holding/vbee-node-shared-lib';
import {
  EMPHASIS_LEVEL,
  VALID_SPEED,
  REGEX,
  TTS_CORE_VERSION,
  VOICE_PROVIDER,
  SYNTHESIS_TYPE,
  VALID_CHARACTERS_LENGTH_REGEX,
} from '../constants';
import { DEFAULT_CLIENT_PAUSE } from '../constants/clientPause';
import logger from '../utils/logger';

const { getVoiceByCodes } = require('./voice');
const { decryptTags } = require('./decrypt');

/** process the text, find <prosody> and normalize its "rate" attribute */
const normalizeSpeedForVbeeVoice = (text) => {
  const $ = cheerio.load(text, { xmlMode: true, decodeEntities: false });

  $('prosody').each((index, element) => {
    const value = $(element).attr('rate').trim();
    if (!value.match(REGEX.VBEE_SPEED)) {
      const rateValue = Number(value.slice(0, -1));
      let rate;

      if (rateValue > 100) {
        rate = `+${rateValue - 100}%`;
      } else {
        rate = `-${100 - rateValue}%`;
      }

      $(element).attr('rate', (i, elem) => elem.replace(value, rate));
    }
  });

  return $.xml().toString();
};

/**
 * @return SYNTHESIS_TYPE.MULTI_VOICE, SYNTHESIS_TYPE.SINGLE_VOICE */
const getSynthesisType = (request) => {
  if (request.sentences?.length) return SYNTHESIS_TYPE.MULTI_VOICE;
  return SYNTHESIS_TYPE.SINGLE_VOICE;
};

const countTextLength = (text, regex) => {
  regex = regex || REGEX.MULTIPLE_TAGS;
  const normalizeText = text.trim().replace(regex, '');
  const textLength = normalizeText.length;
  return textLength;
};

const validateSsmlText = (text) => {
  const $ = cheerio.load(text, { xmlMode: true, decodeEntities: false });

  // const xml = $.xml();
  let isValidXml = true;
  // if (xml !== text) return false;

  $('emphasis').each((index, element) => {
    const level = $(element).attr('level').trim();
    if (!Object.values(EMPHASIS_LEVEL).includes(level)) {
      isValidXml = false;
      return false;
    }

    return true;
  });
  if (!isValidXml) return false;

  $('break').each((index, element) => {
    const time = $(element).attr('time').trim();
    const seconds = time.slice(0, -1);
    const unit = time.slice(-1);

    if (unit !== 's' || seconds < 0.1 || seconds > 60) {
      isValidXml = false;
      return false;
    }

    return true;
  });
  if (!isValidXml) return false;

  $('prosody').each((index, element) => {
    const value = $(element).attr('rate').trim();
    const rate = Number(value.slice(0, -1)) / 100;

    if (rate < VALID_SPEED.MIN || rate > VALID_SPEED.MAX) {
      isValidXml = false;
      return false;
    }

    return true;
  });
  if (!isValidXml) return false;

  return isValidXml;
};

/** some voiceProvider and ttsCoreVersion have different standard of "valid text" */
const validateText = ({ text, voiceProvider, ttsCoreVersion }) => {
  let isValidText = true;

  if (
    voiceProvider === VOICE_PROVIDER.VBEE ||
    voiceProvider === VOICE_PROVIDER.VBEE_VOICE_CLONING
  ) {
    if (ttsCoreVersion !== TTS_CORE_VERSION.NEW) {
      isValidText = !text.match(VALID_CHARACTERS_LENGTH_REGEX);
    } else {
      const pureText = text.replace(REGEX.ADVANCE_TAG, '');
      isValidText = !pureText.match(VALID_CHARACTERS_LENGTH_REGEX);
    }

    if (!isValidText) return false;
  }

  if (ttsCoreVersion === TTS_CORE_VERSION.NEW) {
    isValidText = validateSsmlText(text);
  }

  return isValidText;
};

/** standardize/normalize text (xml.tag prosody rate="", xml.tag break time="") */
const processText = async ({
  text,
  voiceProvider,
  // voiceLanguage,
  ttsCoreVersion,
  paragraphBreak = DEFAULT_CLIENT_PAUSE.PARAGRAPH_BREAK,
  hasClientPause,
}) => {
  // const isVietnameseVoice = voiceLanguage === 'vi-VN';
  let normalizeText = text;
  // change speed for vbee voice from 150% to +50%
  if (
    ttsCoreVersion === TTS_CORE_VERSION.NEW &&
    voiceProvider === VOICE_PROVIDER.VBEE &&
    !!text.match(REGEX.ADVANCE_TAG)
  ) {
    normalizeText = normalizeSpeedForVbeeVoice(normalizeText);
  }

  if (
    hasClientPause &&
    (voiceProvider === VOICE_PROVIDER.VBEE ||
      voiceProvider === VOICE_PROVIDER.VBEE_VOICE_CLONING)
  ) {
    let breakTag;
    const SPACE = ' ';

    if (paragraphBreak === 0) breakTag = SPACE;
    else
      breakTag =
        ttsCoreVersion === TTS_CORE_VERSION.NEW
          ? `<break time="${paragraphBreak}s"/>`
          : `<break time=${paragraphBreak}s/>`;

    normalizeText = normalizeText.replace(REGEX.BREAK_LINE, breakTag);
  }

  return normalizeText;
};

const getValidSampleRates = async (voiceCodes = []) => {
  if (!voiceCodes.length) return [];

  const voices = await getVoiceByCodes(voiceCodes);

  const sampleRates = voices.reduce((acc, curr) => {
    const currSampleRates = curr.sampleRates || [];
    if (!acc.length) return currSampleRates;
    return currSampleRates.filter((sampleRate) => acc.indexOf(sampleRate) >= 0);
  }, []);

  return {
    sampleRates,
    maxSampleRate: sampleRates.length ? Math.max(...sampleRates) : null,
  };
};

/** transform value to miliseconds */
const getMsClientPause = ({
  paragraphBreak = DEFAULT_CLIENT_PAUSE.PARAGRAPH_BREAK,
  sentenceBreak = DEFAULT_CLIENT_PAUSE.SENTENCE_BREAK,
  majorBreak = DEFAULT_CLIENT_PAUSE.MAJOR_BREAK,
  mediumBreak = DEFAULT_CLIENT_PAUSE.MEDIUM_BREAK,
}) => {
  const msParagraphBreak = paragraphBreak * 1000;
  const msSentenceBreak = sentenceBreak * 1000;
  const msMajorBreak = majorBreak * 1000;
  const msMediumBreak = mediumBreak * 1000;

  return {
    paragraphBreak: msParagraphBreak.toString(),
    sentenceBreak: msSentenceBreak.toString(),
    majorBreak: msMajorBreak.toString(),
    mediumBreak: msMediumBreak.toString(),
  };
};

const getTags = ({ tagsString, appId, requestId, sessionId, aesKey }) => {
  let tags = {};
  try {
    tags = JSON.parse(tagsString);
  } catch (error) {
    logger.error(error, {
      ctx: 'CachingRequest',
      appId,
      sessionId,
    });
  }

  if (aesKey) {
    tags = decryptTags({ encryptedTags: tags, aesKey, requestId, sessionId });
  }

  return tags;
};

const getTextFromTemplateAndTags = ({ template, tags }) => {
  const personalTags = template.match(REGEX.CACHING_PERSONAL_TAG);
  if (!personalTags) return template;

  const text = personalTags.reduce((acc, curr) => {
    const tag = curr.slice(1, -1);
    if (tags[tag]) acc = acc.replace(curr, tags[tag]);

    return acc;
  }, template);

  return text;
};

const preCheckSynthesisApiRequest = ({
  app,
  template,
  text,
  sessionId,
  tagsString,
  ttsCoreVersion,
  requestId,
  aesKey,
}) => {
  let textLength;
  let tags = {};

  if (tagsString)
    tags = getTags({
      tagsString,
      appId: app,
      requestId,
      sessionId,
      aesKey,
    });

  const { _id: appId } = app;

  if (!text) text = getTextFromTemplateAndTags({ template, tags });
  if (ttsCoreVersion === TTS_CORE_VERSION.NEW)
    textLength = countTextLength(text, REGEX.ADVANCE_TAG);
  else textLength = countTextLength(text);

  logger.info({ textLength }, { ctx: 'CheckTextLength' });

  return {
    appId,
    text,
    tags,
    textLength,
  };
};

const getSubtitleContent = (content) => {
  const subtitleBlocks = content.split(/\n\s*\n/);
  const parsedSubtitles = [];

  for (const subtitleBlock of subtitleBlocks) {
    /**
     * subtitleBlock: string - subtitle content
     * Example:
        48
        00:03:16,280 --> 00:03:18,907
        <i>Hello, I'm a subtitle.</i>
    */
    const lines = subtitleBlock.trim().split('\n');

    // first line is index, second line is time, third line is subtitle content
    if (lines.length >= 3) {
      const subtitleContent = TextHelper.removeHtmlTags(
        lines.slice(2).join(''),
      );
      parsedSubtitles.push(subtitleContent);
    }
  }

  return parsedSubtitles;
};

module.exports = {
  getSynthesisType,
  countTextLength,
  validateSsmlText,
  validateText,
  processText,
  getValidSampleRates,
  getMsClientPause,
  getTags,
  getTextFromTemplateAndTags,
  preCheckSynthesisApiRequest,
  getSubtitleContent,
};
