version: '3.8'

services:
  tts-api:
    build:
      context: .
      dockerfile: Dockerfile
      secrets:
        - npm_token
    ports:
      - "8010:8010"
    environment:
      - NODE_ENV=production
      - PORT=8010
      - ENVIRONMENT=dev
      - MONGO_URI=${MONGO_URI:-mongodb://mongo:27017/tts-gateway}
      - REDIS_URI=${REDIS_URI:-redis://redis:6379}
      - IAM_URL=${IAM_URL:-https://dev-accounts.vbee.vn}
      - IAM_REALM=${IAM_REALM:-vbee-holding}
      - IAM_VALID_CLIENT_IDS=${IAM_VALID_CLIENT_IDS:-vbee-tts-crm,vbee-tts-back-office}
      - KAFKA_CLIENT_ID=${KAFKA_CLIENT_ID:-tts-api-docker}
      - KAFKA_BROKERS=${KAFKA_BROKERS:-kafka:9092}
      - LOG_SHORTEN=${LOG_SHORTEN:-false}
      - SYNTHESIS_TTS_API=${SYNTHESIS_TTS_API:-true}
      - DEFAULT_AWS_REGION=${DEFAULT_AWS_REGION:-ap-southeast-1}
      - MULTI_ZONE=${MULTI_ZONE:-0}
      - GROWTH_BOOK_API_HOST=${GROWTH_BOOK_API_HOST:-https://dev-api-growthbook.vbee.vn}
      - GROWTH_BOOK_CLIENT_KEY=${GROWTH_BOOK_CLIENT_KEY}
      - LOADING_FEATURES_REALTIME_INTERVAL=${LOADING_FEATURES_REALTIME_INTERVAL:-1000}
      - VC_SYNTHESIS_FUNCTION=${VC_SYNTHESIS_FUNCTION}
      - MICROSOFT_REGION=${MICROSOFT_REGION:-southeastasia}
      - MICROSOFT_SPEECH_KEY=${MICROSOFT_SPEECH_KEY}
      - PRIVATE_RSA_KEY=${PRIVATE_RSA_KEY}
      - STUDIO_TTS_VERSION_DEFAULT=${STUDIO_TTS_VERSION_DEFAULT:-1.0}
      - IS_TTS_GATE=${IS_TTS_GATE:-true}
      - TTS_GATE_API_URL=${TTS_GATE_API_URL:-http://localhost:80}
      - TTS_CORE_URL=${TTS_CORE_URL}
      - TTS_CORE_APP_ID=${TTS_CORE_APP_ID}
      - TTS_CORE_ACCESS_TOKEN=${TTS_CORE_ACCESS_TOKEN}
    depends_on:
      - mongo
      - redis
      - kafka
    networks:
      - tts-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:8010/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  mongo:
    image: mongo:7.0-jammy
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=root
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=tts-gateway
    volumes:
      - mongo_data:/data/db
    networks:
      - tts-network
    restart: unless-stopped

  redis:
    image: redis:7.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tts-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - tts-network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    networks:
      - tts-network
    restart: unless-stopped

volumes:
  mongo_data:
  redis_data:

networks:
  tts-network:
    driver: bridge

secrets:
  npm_token:
    file: .npm_token
