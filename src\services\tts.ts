import requestDao from '../daos/request';

const { findTts, getTtsFromRequestIdInRedis } = require('../daos/tts');
const { getTimestampWords } = require('./getTimestampWords');

const getTts = async ({ search, searchFields, dateField, query, offset, limit, fields, sort }, requestId) => {
  const request = await requestDao.findRequest({ sessionId: requestId });
  query.requestId = request._id;

  const { tts, total } = await findTts({
    search,
    searchFields,
    dateField,
    query,
    offset,
    limit,
    fields,
    sort,
  });

  if (!tts?.length) return { tts: [], total: 0 };

  return { tts, total };
};

const getAllTimestamps = async (requestId) => {
  const ttsList = await getTtsFromRequestIdInRedis(requestId);

  const sortTtsList = ttsList.sort((a, b) => a.index - b.index || a.subIndex - b.subIndex);

  const timestampsArr = sortTtsList.reduce((acc, curr) => {
    const { timestampWords } = curr;
    if (timestampWords) return [...acc, timestampWords];

    return acc;
  }, []);

  const timestampsWords = getTimestampWords(timestampsArr);

  return timestampsWords;
};

module.exports = { getTts, getAllTimestamps };
