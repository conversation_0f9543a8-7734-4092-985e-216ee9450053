import { createClient, RedisClientType } from 'redis';
import { Caching } from '@gurucore/lakdak';
import {
  RedisDistributedPositiveCounter,
  RedisDistributedQueue,
  RedisRepository,
  CacheManagerFactory,
} from '@vbee-holding/vbee-node-shared-lib';

import logger from '../../utils/logger';
import configs from '../../configs';

const DELAY_TO_RECONNECT = 10000;

// The singleton pattern ensures that all parts of the application use the same Redis instance, and the event-driven approach allows for automatic recovery from connection issues.
const Singleton = {
  /** single connection to Redis, can be shared across multiple wrapper (Manager, GlobalCounter, GlobalQueue, ...) */
  redisClient: createClient({ url: configs.REDIS_URI }),
} as {
  redisClient: RedisClientType;
  RedisRepo: RedisRepository;
  GlobalCounter: RedisDistributedPositiveCounter;
  GlobalQueue: RedisDistributedQueue;
  Manager: Caching.CacheManager;
};

/** init the Caching with preconfigured parameters from env, setup the Singleton */
async function init() {
  Singleton.GlobalCounter = new RedisDistributedPositiveCounter(
    Singleton.redisClient,
    logger,
    5000,
  );
  Singleton.GlobalCounter.connect();

  Singleton.GlobalQueue = new RedisDistributedQueue(
    Singleton.redisClient,
    logger,
    10000,
  );
  Singleton.GlobalQueue.connect();

  Singleton.Manager = await CacheManagerFactory.createRedisCache({
    // @ts-ignore
    redisConnect: Singleton.redisClient,
    descriptiveName: 'vbee-tts-api-global-redis-cache-manager',
  });

  Singleton.RedisRepo = new RedisRepository(
    Singleton.redisClient,
    logger,
    10000,
  );
  Singleton.RedisRepo.connect();

  // TODO: CachingService: Listens for error events and automatically retries initialization after a 5-second delay
  // Error handling: Proper logging and graceful retry mechanism to maintain service availability
  // Listen for "connecterror, error, timeouterror" events and handle reconnection
  Singleton.RedisRepo.on('connecterror', (err) => {
    logger.warn(
      `CachingService: Redis error detected, will retry initialization in ${DELAY_TO_RECONNECT}`,
      {
        ctx: 'CachingService',
        // @ts-ignore
        error: err.message,
      },
    );
    // TODO: send instant notification: RedisError

    setTimeout(() => {
      // TODO: Retry initialization after a delay
      // reinit();
    }, DELAY_TO_RECONNECT);
  });

  logger.info('CachingService: init successfully', {
    ctx: 'CachingService',
  });

  return Singleton;
}

async function reinit() {
  logger.info('Reinitializing Redis connection', {
    ctx: 'CachingService',
  });
  // TODO: send instant notification: Reinit

  try {
    return await init();
  } catch (error) {
    logger.error('Failed to reinitialize Redis connection', {
      ctx: 'CachingService',
      stack: error.stack,
    });

    // TODO: send instant notification: Reinit failed, also should count retry times
    return null; // Return null or throw the error if reinitialization fails
  }
}

/** The Global scope Caching Service */
export default {
  init,
  reinit,

  // NOTE: because we're using CommonJS, JavaScript doesn't have true "async require" - require() is always synchronous.
  // We need to have The lazy evaluation approach to ensure the initialization order (caching init first, then other services-that-use-caching-can-access-initialized-object) is correct.
  // if we access {Manager} = require("caching"), it will be the undefined (not init) value.
  // With the getter, each time you access  Manager, it calls the getter function which returns the current initialized value.

  // NOTE: IMPORTANT
  // const Caching = require('../services/caching'); ==> undefined
  // The issue is with destructuring vs property access and how JavaScript handles getters.
  // This destructures the Manager property at import time. Since Manager is a getter function, JavaScript calls the getter once during destructuring and stores the returned value (which is undefined at import time because init() hasn't run yet).

  // const { Manager } = require('../services/caching'); ==> yield the correct object
  // When you imports the whole module object. When you access Caching.Manager, JavaScript calls the getter function each time, so it gets the current initialized value.

  /**
   * This is low level Redis repository when you really need to interact using Redis API directly (instead of using standard Caching.Manager API).
   * For normal caching operation, it is RECOMMENDED to use Caching.Manager if possible.
   * @description Must be import and access as Caching.RedisRepo.
   * @type {RedisRepository}
   */
  get RedisRepo() {
    return Singleton.RedisRepo;
  },
  /**
   * Distributed Counter using Redis, work across multiple vbee-tts-api instances
   * @description Must be import and access as Caching.GlobalCounter.
   * @type {RedisDistributedPositiveCounter}
   */
  get GlobalCounter() {
    return Singleton.GlobalCounter;
  },
  /**
   * Distributed Queue using Redis, work across multiple vbee-tts-api instances
   * @description Must be import and access as Caching.GlobalQueue.
   * @type {RedisDistributedQueue}
   */
  get GlobalQueue() {
    return Singleton.GlobalQueue;
  },

  /**
   * Global Cache Manager, using Redis. Recommended to use for normal caching operation. Auto serialize/deserialize when we perform get/set with Object value
   * @description Must be import and access as Caching.Manager.
   * @type {CachingBase.CacheManager}
   */
  get Manager() {
    return Singleton.Manager;
  },
};
