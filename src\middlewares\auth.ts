const asyncMiddleware = require('./async');
const CustomError = require('../errors/CustomError');
const codes = require('../errors/code');
const authService = require('../services/auth');

const auth = async (req, res, next) => {
  const { authorization } = req.headers;
  if (!authorization) throw new CustomError(codes.UNAUTHORIZED);

  const [tokenType, accessToken] = authorization.split(' ');

  if (tokenType !== 'Bearer') throw new Error(codes.UNAUTHORIZED);

  const data = await authService.verifyAccessToken(accessToken);
  const { sub: userId, email, name, phoneNumber } = data;

  req.userId = userId;
  req.user = { userId, email, name, phoneNumber };

  return next();
};

const authAPI = async (req, res, next) => {
  const receivedAt = new Date();
  const { authorization } = req.headers;
  const { appId } = req.body;

  if (!appId) throw new CustomError(codes.UNAUTHORIZED);
  if (!authorization) throw new CustomError(codes.UNAUTHORIZED);

  const [tokenType, accessToken] = authorization.split(' ');

  if (tokenType !== 'Bearer') throw new Error(codes.UNAUTHORIZED);

  const app = await authService.verifyAccessTokenAPI(appId, accessToken);
  req.apiApp = app;
  req.receivedAt = receivedAt;

  next();
};

module.exports = {
  auth: asyncMiddleware(auth),
  authAPI: asyncMiddleware(authAPI),
};
