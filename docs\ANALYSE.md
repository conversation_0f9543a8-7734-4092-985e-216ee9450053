# Project Analysis and Architecture

- Text-to-Speech (TTS) API service built with Node.js
- Drive request to the internal TTS engine, or external TTS engine

## **Core Technologies**

- **Runtime**: Node.js 20.19 (Alpine Linux container)
- **Framework**: Express.js with comprehensive middleware stack
- **Authentication**: JWT-based with IAM integration
- **Cloud Services**: AWS (S3, Lambda), Google Cloud Storage
- **Monitoring**: Sentry for error tracking, New Relic for APM
- **Multi-stage Docker Build**: Optimized for production
- Intelligent Caching: Multi-layer caching strategy using Redis for both request data and audio files
- Cloud-Native Design: Built for AWS with Lambda functions, S3 storage, and multi-zone deployment
- Flexible Voice Engine: Supports multiple TTS engines and voice providers with dynamic switching


## Important Features & Capabilities

1. Multi-Modal TTS Support
Standard TTS: Basic text-to-speech conversion
Caching TTS: High-performance cached synthesis
Dubbing: Advanced multi-voice audio dubbing
Real-time Processing: Low-latency synthesis options

2. Advanced Voice Management
Multiple voice providers (VBEE, third-party)
Voice code mapping and validation
Speed rate control (0.25x - 1.9x)
Multiple audio formats (MP3, WAV, etc.)
Sample rate options (8kHz - 48kHz)

3. Scalability Features
Asynchronous Processing: Kafka-based message queuing
Multi-zone Deployment: AWS region distribution
Caching Strategy: Redis-based performance optimization
Load Balancing: Distributed processing across zones



#### **Request Processing Pipeline**

- Text Preprocessing: Normalization, sentence tokenization
- Voice Processing: Multiple voice providers and configurations
- Synthesis: Asynchronous audio generation
- Post-processing: Audio joining, caching, delivery


## Detailed architectural

```mermaid
graph TB
    %% Client Layer
    Client[Client Applications]
    
    %% API Gateway Layer
    subgraph "API Layer"
        Express[Express.js Server]
        Auth[Authentication Middleware]
        Validation[Request Validation]
        RateLimit[Rate Limiting]
    end
    
    %% Core Services
    subgraph "Core Services"
        APIController[API Controller]
        CachingController[Caching Controller]
        TTSController[TTS Controller]
        DubbingController[Dubbing Controller]
    end
    
    %% Processing Pipeline
    subgraph "Processing Pipeline"
        Preprocessing[Text Preprocessing]
        VoiceProcessing[Voice Processing]
        Synthesis[TTS Synthesis]
        AudioJoining[Audio Joining]
        PostProcessing[Post Processing]
    end
    
    %% Message Queue
    subgraph "Message Queue (Kafka)"
        Producer[Kafka Producer]
        Consumer[Kafka Consumer]
        Topics[Topics:<br/>- Sentence Tokenization<br/>- Synthesis Success/Failure<br/>- Audio Join<br/>- Caching]
    end
    
    %% Storage Layer
    subgraph "Storage Layer"
        MongoDB[(MongoDB)]
        Redis[(Redis Cache)]
        S3[AWS S3<br/>Audio Storage]
    end
    
    %% External Services
    subgraph "External Services"
        TTSCore[TTS Core Service]
        IAM[Identity & Access Management]
        VoiceProviders[Voice Providers<br/>- VBEE<br/>- Third-party]
    end
    
    %% AWS Infrastructure
    subgraph "AWS Infrastructure"
        Lambda[Lambda Functions]
        CloudRun[Cloud Run]
        MultiZone[Multi-Zone Processing]
    end
    
    %% Connections
    Client --> Express
    Express --> Auth
    Auth --> Validation
    Validation --> RateLimit
    RateLimit --> APIController
    RateLimit --> CachingController
    RateLimit --> TTSController
    RateLimit --> DubbingController
    
    APIController --> Preprocessing
    CachingController --> Redis
    Preprocessing --> VoiceProcessing
    VoiceProcessing --> Synthesis
    Synthesis --> AudioJoining
    AudioJoining --> PostProcessing
    
    Synthesis --> Producer
    Producer --> Topics
    Topics --> Consumer
    Consumer --> PostProcessing
    
    APIController --> MongoDB
    CachingController --> MongoDB
    TTSController --> MongoDB
    
    Preprocessing --> Redis
    Synthesis --> Redis
    PostProcessing --> Redis
    
    PostProcessing --> S3
    
    Auth --> IAM
    Synthesis --> TTSCore
    VoiceProcessing --> VoiceProviders
    
    Synthesis --> Lambda
    Synthesis --> CloudRun
    Lambda --> MultiZone
    CloudRun --> MultiZone
    
    style Client fill:#e1f5fe
    style Express fill:#f3e5f5
    style MongoDB fill:#e8f5e8
    style Redis fill:#ffebee
    style S3 fill:#fff3e0
    style Kafka fill:#f1f8e9
```


## database relationships:

### **Models**

- Request Model: Tracks TTS synthesis requests with comprehensive metadata
- TTS Model: Manages individual text-to-speech conversion tasks
- App Model: Handles API client authentication and permissions
- AWS Zone Model: Manages distributed processing zones

