#!/bin/bash

# Docker build script for TTS API

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if npm token file exists
if [ ! -f ".npm_token" ]; then
    print_error "NPM token file (.npm_token) not found!"
    print_warning "Please create .npm_token file with your GitHub NPM token"
    print_warning "Example: echo 'your_github_token_here' > .npm_token"
    exit 1
fi

# Parse command line arguments
COMMAND=${1:-build}
ENVIRONMENT=${2:-production}

case $COMMAND in
    "build")
        print_status "Building TTS API Docker image..."
        if [ "$ENVIRONMENT" = "dev" ]; then
            print_status "Building development image..."
            docker build -f Dockerfile.dev --secret id=npm_token,src=.npm_token -t tts-api:dev .
        else
            print_status "Building production image..."
            docker build --secret id=npm_token,src=.npm_token -t tts-api:latest .
        fi
        print_success "Docker image built successfully!"
        ;;
    
    "run")
        print_status "Starting TTS API with Docker Compose..."
        if [ "$ENVIRONMENT" = "dev" ]; then
            print_status "Starting development environment..."
            docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build
        else
            print_status "Starting production environment..."
            docker-compose up --build
        fi
        ;;
    
    "stop")
        print_status "Stopping TTS API containers..."
        docker-compose down
        print_success "Containers stopped!"
        ;;
    
    "clean")
        print_status "Cleaning up Docker resources..."
        docker-compose down -v --remove-orphans
        docker image prune -f
        print_success "Cleanup completed!"
        ;;
    
    "logs")
        print_status "Showing TTS API logs..."
        docker-compose logs -f tts-api
        ;;
    
    "shell")
        print_status "Opening shell in TTS API container..."
        docker-compose exec tts-api sh
        ;;
    
    "test")
        print_status "Running tests in Docker container..."
        docker run --rm tts-api:latest npm test
        ;;
    
    *)
        echo "Usage: $0 {build|run|stop|clean|logs|shell|test} [dev|production]"
        echo ""
        echo "Commands:"
        echo "  build [dev|production]  - Build Docker image"
        echo "  run [dev|production]    - Start application with Docker Compose"
        echo "  stop                    - Stop all containers"
        echo "  clean                   - Stop containers and clean up resources"
        echo "  logs                    - Show application logs"
        echo "  shell                   - Open shell in running container"
        echo "  test                    - Run tests in container"
        echo ""
        echo "Examples:"
        echo "  $0 build                # Build production image"
        echo "  $0 build dev            # Build development image"
        echo "  $0 run                  # Start production environment"
        echo "  $0 run dev              # Start development environment"
        exit 1
        ;;
esac
