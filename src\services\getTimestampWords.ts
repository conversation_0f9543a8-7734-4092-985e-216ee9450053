// Input: [{ start: 0, end: 1, detail: [{ word: 'hello', start: 0, end: 1 }] }, {start: 0, end: 1}, { start: 0, end: 1, detail: [{ word: 'world', start: 0, end: 1 }] }

const { flattenArray } = require('../utils/array');
import logger from '../utils/logger';

// Output: [{ word: 'hello', start: 0, end: 1 }, { word: 'world', start: 2, end: 3 }]
const getTimestampWords = (data) => {
  try {
    let previousEnd = 0;
    const flattenedData = flattenArray(data);
    const timestampsWords = [];

    flattenedData.forEach((sentence) => {
      sentence.start = previousEnd;
      sentence.end += sentence.start;
      previousEnd = sentence.end;

      if (sentence.detail && Array.isArray(sentence.detail)) {
        sentence.detail.forEach((word) =>
          timestampsWords.push({
            word: word.word,
            start: word.start + sentence.start,
            end: word.end + sentence.start,
          }),
        );
      }
    });

    return timestampsWords;
  } catch (error) {
    logger.warn(error, { ctx: 'GetTimestampWords', data });
    return [];
  }
};

module.exports = { getTimestampWords };
