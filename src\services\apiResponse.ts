const https = require('https');
const retry = require('retry');
require('dotenv').config();
const { default: axios } = require('axios');
const snakecaseKeys = require('snakecase-keys');
import configs from '../configs';
import {
  RESPONSE_TYPE,
  OUTPUT_TYPE,
  API_RESPONSE_TYPE,
  V3_API_TYPE,
} from '../constants';
const getErrorMessage = require('../errors/message');
import logger from '../utils/logger';
const { createCallbackResult } = require('../daos/callbackResult');

const sendBinaryResponse = async ({ audioLink, audioType, res }) => {
  res.writeHead(200, {
    'Content-Type':
      audioType.indexOf('wav') !== -1 ? 'audio/x-wav' : 'audio/mpeg',
    'Content-disposition': `attachment; filename=${
      audioLink.split('/').slice(-1)[0]
    }`,
  });
  const response = await axios({
    url: audioLink,
    method: 'GET',
    responseType: 'stream',
  });
  response.data.pipe(res);
};

const sendCallback = async ({ callbackUrl, payload, requestId, sessionId }) => {
  const httpsAgent = new https.Agent({ rejectUnauthorized: false });

  const response = await axios({
    method: 'POST',
    url: callbackUrl,
    data: payload,
    httpsAgent,
  });

  logger.info(JSON.stringify(response.data), {
    ctx: 'SendApiResponse',
    requestId,
    sessionId,
  });

  return response;
};

// Use retry to send callback
const executeCallback = async ({ request, payload }) => {
  const { fromVn, requestId, sessionId } = request;
  let statusCode;
  let result;

  // ADVISE: refactor note: original code does not has VN_URL in configs. Maybe this func has no usage?
  const callbackUrl = fromVn
    ? `${configs.VN_URL}/api/v1/callback?callbackUrl=${encodeURIComponent(
        request.callbackUrl,
      )}`
    : request.callbackUrl;
  return new Promise((resolve) => {
    const operation = retry.operation({
      retries: 5, // Maximum 5 retries
      factor: 2, // Exponential backoff factor
      minTimeout: 2000, // Start with 2 seconds delay
      maxTimeout: 30000, // Max 30 seconds between retries
      randomize: true, // Add some randomization to prevent thundering herd
    });

    operation.attempt(async (currentAttempt) => {
      try {
        const response = await sendCallback({
          callbackUrl,
          payload,
          requestId,
          sessionId,
        });
        statusCode = response.status;
        result = response.data;
        resolve({ statusCode, result });
      } catch (err) {
        if (operation.retry(err)) {
          logger.warn(
            `Callback attempt ${currentAttempt} failed, retrying...`,
            { requestId, sessionId },
          );

          return;
        }

        const mainError = operation.mainError();
        statusCode = mainError?.response?.status;
        result = mainError?.message;
        resolve({ statusCode, result });
      }
    });
  });
};
const getTypeResponseData = (v3ApiType, responseType) => {
  if (!v3ApiType) return API_RESPONSE_TYPE.STUDIO_API;
  if (
    v3ApiType === V3_API_TYPE.ENTERPRISE &&
    responseType === RESPONSE_TYPE.INDIRECT
  )
    return API_RESPONSE_TYPE.V3_ENTERPRISE_INDIRECT;
  if (
    v3ApiType === V3_API_TYPE.ENTERPRISE &&
    responseType === RESPONSE_TYPE.DIRECT
  )
    return API_RESPONSE_TYPE.V3_ENTERPRISE_DIRECT;
  if (v3ApiType === V3_API_TYPE.ARTICLE) return API_RESPONSE_TYPE.V3_ARTICLE;
  if (v3ApiType === V3_API_TYPE.PERSONAL) return API_RESPONSE_TYPE.V3_PERSONAL;
  return '';
};

const sendApiResponse = async ({ request, res, errorCode, errorMessage }) => {
  const {
    requestId,
    outputType,
    audioType,
    responseType,
    v3ApiType,
    fromVn,
    type,
    cachingTime,
    sessionId,
    isFromCache,
    timestampWords,
  } = request;

  logger.info(
    {
      requestId,
      outputType,
      audioType,
      responseType,
      fromVn,
      type,
      cachingTime,
      sessionId,
      isFromCache,
      errorCode,
      errorMessage,
    },
    { ctx: 'SendApiResponse' },
  );

  let payload = {
    requestId,
    sessionId,
    errorCode,
    errorMessage: errorMessage || getErrorMessage(errorCode) || undefined,
    status: errorCode && errorMessage ? 0 : 1,
    timestampWords,
  };

  // Response Data with API V3: ENTERPRISE and ARTICLE

  // response data success
  if (!errorCode && !errorMessage) {
    const {
      app,
      characters,
      voiceCode,
      speed,
      sampleRate,
      bitrate,
      createdAt,
      status,
      audioLink,
    } = request;

    const responseDataType = getTypeResponseData(v3ApiType, responseType);

    if (
      request.responseType === RESPONSE_TYPE.DIRECT &&
      outputType === OUTPUT_TYPE.BINARY &&
      !fromVn &&
      res
    ) {
      await sendBinaryResponse({ audioLink, audioType, res });

      return;
    }

    switch (responseDataType) {
      case API_RESPONSE_TYPE.STUDIO_API:
        payload = {
          appId: app,
          requestId,
          characters,
          voiceCode,
          audioType,
          speedRate: speed,
          bitrate,
          sampleRate,
          createdAt,
          status,
          audioLink,
          isFromCache,
          sessionId,
          timestampWords,
        };
        break;

      default:
        break;
    }
  }

  const sendPayload = snakecaseKeys(payload, { deep: true });

  if (request.responseType === RESPONSE_TYPE.DIRECT && res) {
    logger.info('Send response to client', { ctx: 'SendApiResponse' });
    res.send(sendPayload);
    delete global.REQUEST_DIRECT[requestId];

    return;
  }

  const response = await executeCallback({
    request,
    payload: sendPayload,
  });

  createCallbackResult({
    requestId,
    callbackUrl: request.callbackUrl,
    payload: sendPayload,
    statusCode: response.statusCode,
    result: response.result,
  });
};

module.exports = { sendApiResponse };
