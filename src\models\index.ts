const mongoose = require('mongoose');
import configs from '../configs';
import logger from '../utils/logger';

const initMongoDB = async () => {
  const mongooseInstance = mongoose.set('strictQuery', true);
  await mongooseInstance.connect(configs.MONGO_URI || '', { autoIndex: false });

  mongooseInstance.connection
    .on('error', (err) => {
      logger.error(`Connect error to MongoDB: ${configs.MONGO_URI}`, {
        ctx: 'MongoDB',
        stack: err.stack,
      });
      process.exit();
    })
    .once('open', () => {
      logger.info(`Connected to MongoDB: ${configs.MONGO_URI}`, {
        ctx: 'MongoDB',
      });
    });

  logger.info('MongoDB: Init successfully,', { ctx: 'MongoDB' });

  return mongooseInstance;
};

module.exports = { initMongoDB };
