{"hn_female_maiphuong_news_48k-d": "hn_female_ma<PERSON>huong_vdts_48k-fhg", "hn_female_maiphuong_vdts_48k-thg": "hn_female_ma<PERSON>huong_vdts_48k-fhg", "hn_female_maiphuong_vdts_48k_cs-thg": "hn_female_ma<PERSON>huong_vdts_48k-fhg", "vbee-tts-voice-hn_female_maiphuong_news_48k-h": "hn_female_ma<PERSON>huong_vdts_48k-fhg", "vbee-tts-voice-hn_male_manhdung_news_48k-h": "hn_male_manhdung_news_48k-fhg", "hn_male_manhdung_news_48k-d": "hn_male_manhdung_news_48k-fhg", "hn_male_manhdung_news_48k-thg": "hn_male_manhdung_news_48k-fhg", "hn_male_manhdung_news_48k_cs-thg": "hn_male_manhdung_news_48k-fhg", "hn_female_ngochuyen_news_48k-thg": "hn_female_ngochuyen_full_48k-fhg", "hn_female_ngochuyen_news_48k-d": "hn_female_ngochuyen_full_48k-fhg", "hn_female_ngochuyen_fast_news_48k-thg": "hn_female_ngochuyen_full_48k-fhg", "vbee-tts-voice-hn_female_ngochuyen_news_48k-h": "hn_female_ngochuyen_full_48k-fhg", "vbee-tts-voice-hn_female_thuylinh_news_48k-h": "", "hue_female_huonggiang_news_48k-thg": "hue_female_huonggiang_full_48k-fhg", "hue_female_huonggiang_news_48k-d": "hue_female_huonggiang_full_48k-fhg", "hue_female_huonggiang_news_48k_cs-thg": "hue_female_huonggiang_full_48k-fhg", "hue_male_duyphuong_news_48k-d": "hue_male_duyphuong_full_48k-fhg", "sg_female_duyphuong_fast_news_48k_cs-thg": "hue_male_duyphuong_full_48k-fhg", "sg_female_ngochuyen_fast_news_48k_cs-thg": "", "vbee-tts-voice-sg_male_minhhoang_dial_48k-h": "sg_male_minhhoang_full_48k-fhg", "sg_male_minhhoang_news_48k-d": "sg_male_minhhoang_full_48k-fhg", "sg_female_minhhoang_fast_news_48k_cs-thg": "sg_male_minhhoang_full_48k-fhg", "sg_male_minhhoang_news_48k-thg": "sg_male_minhhoang_full_48k-fhg", "sg_male_minhhoang_fast_news_48k_cs-thg": "sg_male_minhhoang_full_48k-fhg", "sg_female_lantrinh_fast_vdts_48k_cs-thg": "sg_female_lantrinh_vdts_48k-fhg", "vbee-tts-voice-sg_female_thaotrinh_news_48k-h": "sg_female_thaotri<PERSON>_full_48k-fhg", "sg_female_thaotrinh_news_48k-d": "sg_female_thaotri<PERSON>_full_48k-fhg", "sg_female_thaotrinh_fast_news_48k-thg": "sg_female_thaotri<PERSON>_full_48k-fhg", "sg_female_thaotrinh_fast_news_48k_cs-thg": "sg_female_thaotri<PERSON>_full_48k-fhg", "vbee-tts-voice-sg_female_thaotrinh_dial_48k-h": "sg_female_thaotri<PERSON>_full_48k-fhg", "en-US-Wavenet-A-MALE": "", "en-US-Wavenet-D-MALE": "en-US-Wavenet-D", "en-US-Wavenet-F-FEMALE": "en-US-Wavenet-F", "en-US-Standard-B-MALE": "", "en-US-Standard-D-MALE": "en-US-Standard-D", "en-US-Standard-E-FEMALE": "en-US-Standard-E", "en-IN-Wavenet-A-FEMALE": "en-IN-Wavenet-A", "en-IN-Wavenet-B-MALE": "en-IN-Wavenet-B", "en-IN-Wavenet-C-MALE": "en-IN-Wavenet-C", "en-IN-Wavenet-D-FEMALE": "en-IN-Wavenet-D", "en-IN-Standard-A-FEMALE": "en-IN-Standard-A", "en-IN-Standard-B-MALE": "en-IN-Standard-B", "en-IN-Standard-C-MALE": "en-IN-Standard-C", "en-IN-Standard-D-FEMALE": "en-IN-Standard-D", "en-GB-Wavenet-A-FEMALE": "en-GB-Wavenet-A", "en-GB-Wavenet-B-MALE": "en-GB-Wavenet-B", "en-GB-Wavenet-C-FEMALE": "en-GB-Wavenet-C", "en-GB-Wavenet-D-MALE": "en-GB-Wavenet-D", "en-GB-Standard-A-FEMALE": "en-GB-Standard-A", "en-GB-Standard-B-MALE": "en-GB-Standard-B", "en-GB-Standard-C-FEMALE": "en-GB-Standard-C", "en-GB-Standard-D-MALE": "en-GB-Standard-D", "en-AU-Standard-A-FEMALE": "en-AU-Standard-A", "en-AU-Standard-B-MALE": "en-AU-Standard-B", "en-AU-Standard-C-FEMALE": "en-AU-Standard-A", "en-AU-Standard-D-MALE": "en-AU-Standard-D", "en-AU-Wavenet-A-FEMALE": "en-AU-Wavenet-A", "en-AU-Wavenet-B-MALE": "en-AU-Wavenet-B", "en-AU-Wavenet-C-FEMALE": "en-AU-Wavenet-C", "en-AU-Wavenet-D-MALE": "en-AU-Wavenet-D", "es-ES-Standard-A-FEMALE": "es-ES-Standard-A", "ko-KR-Wavenet-B-FEMALE": "ko-KR-Wavenet-B", "ko-KR-Wavenet-C-MALE": "ko-KR-Wavenet-C", "ko-KR-Standard-A-FEMALE": "ko-KR-Standard-A", "ko-KR-Standard-B-FEMALE": "ko-KR-Standard-B", "ko-KR-Standard-C-MALE": "ko-KR-Standard-C", "ko-KR-Standard-D-MALE": "ko-KR-Standard-D", "kn-IN-Standard-A-FEMALE": "kn-IN-Standard-A", "kn-IN-Standard-B-MALE": "kn-IN-Standard-B", "cmn-CN-Wavenet-D-FEMALE": "", "cmn-CN-Standard-D-FEMALE": "cmn-CN-Standard-D", "cmn-CN-Standard-B-MALE": "cmn-CN-Standard-B", "cmn-CN-Wavenet-B-MALE": "", "fr-FR-Wavenet-B-MALE": "", "fr-FR-Wavenet-C-FEMALE": "", "fr-FR-Standard-A-FEMALE": "fr-FR-Standard-A", "fr-FR-Standard-B-MALE": "fr-FR-Standard-B", "fr-FR-Standard-C-FEMALE": "fr-FR-Standard-C", "fr-FR-Standard-D-MALE": "fr-FR-Standard-D", "fr-FR-Standard-E-FEMALE": "fr-FR-Standard-E", "ja-JP-Wavenet-A-FEMALE": "", "ja-JP-Wavenet-D-MALE": "", "ja-JP-Standard-A-FEMALE": "ja-JP-Standard-A", "ja-JP-Standard-B-FEMALE": "ja-JP-Standard-B", "ja-JP-Standard-C-MALE": "ja-JP-Standard-C", "ja-JP-Standard-D-MALE": "ja-JP-Standard-D", "vi-VN-Wavenet-A-FEMALE": "", "de-DE-Wavenet-F-FEMALE": "", "de-DE-Standard-A-FEMALE": "de-DE-Standard-A", "de-DE-Standard-B-MALE": "de-DE-Standard-B", "de-DE-Standard-E-MALE": "de-DE-Standard-E", "de-DE-Standard-F-FEMALE": "de-DE-Standard-F", "hi-IN-Standard-A-FEMALE": "hi-IN-Standard-A", "hi-IN-Standard-B-MALE": "hi-IN-Standard-B", "hi-IN-Standard-C-MALE": "hi-IN-Standard-C", "hi-IN-Standard-D-FEMALE": "hi-IN-Standard-D", "hi-IN-Wavenet-D-FEMALE": "", "hu-HU-Standard-A-FEMALE": "hu-HU-Standard-A", "ar-XA-Standard-A-FEMALE": "ar-XA-Standard-A", "ar-XA-Standard-B-MALE": "ar-XA-Standard-B", "ar-XA-Standard-C-MALE": "ar-XA-Standard-C", "ar-XA-Standard-D-FEMALE": "ar-XA-Standard-D", "bn-IN-Standard-A-FEMALE": "bn-IN-Standard-A", "bn-IN-Standard-B-MALE": "bn-IN-Standard-B", "cs-CZ-Standard-A-FEMALE": "cs-CZ-Standard-A", "da-DK-Standard-A-FEMALE": "da-DK-Standard-A", "da-DK-Standard-C-MALE": "da-DK-Standard-C", "da-DK-Standard-D-FEMALE": "da-DK-Standard-D", "da-DK-Standard-E-FEMALE": "da-DK-Standard-E", "el-GR-Standard-A-FEMALE": "el-GR-Standard-A", "fil-PH-Standard-A-FEMALE": "fil-PH-Standard-A", "fil-PH-Standard-B-FEMALE": "fil-PH-Standard-B", "fil-PH-Standard-C-MALE": "fil-PH-Standard-C", "fil-PH-Standard-D-MALE": "fil-PH-Standard-D", "fi-FI-Standard-A-FEMALE": "fi-FI-Standard-A", "fr-CA-Standard-A-FEMALE": "fr-CA-Standard-A", "fr-CA-Standard-B-MALE": "fr-CA-Standard-B", "fr-CA-Standard-C-FEMALE": "fr-CA-Standard-C", "fr-CA-Standard-D-MALE": "fr-CA-Standard-D", "gu-IN-Standard-A-FEMALE": "gu-IN-Standard-A", "gu-IN-Standard-B-MALE": "gu-IN-Standard-B", "id-ID-Standard-A-FEMALE": "id-ID-Standard-A", "id-ID-Standard-B-MALE": "id-ID-Standard-B", "id-ID-Standard-C-MALE": "id-ID-Standard-C", "id-ID-Standard-D-FEMALE": "id-ID-Standard-D", "it-IT-Standard-A-FEMALE": "it-IT-Standard-A", "it-IT-Standard-B-FEMALE": "it-IT-Standard-B", "it-IT-Standard-C-MALE": "it-IT-Standard-C", "it-IT-Standard-D-MALE": "it-IT-Standard-D", "ml-IN-Standard-A-FEMALE": "ml-IN-Standard-A", "ml-IN-Standard-B-MALE": "ml-IN-Standard-B", "nb-NO-Standard-A-FEMALE": "nb-NO-Standard-A", "nb-NO-Standard-B-MALE": "nb-NO-Standard-B", "nb-NO-Standard-C-FEMALE": "nb-NO-Standard-C", "nb-NO-Standard-D-MALE": "nb-NO-Standard-D", "nb-no-Standard-E-FEMALE": "nb-no-Standard-E", "nl-NL-Standard-A-FEMALE": "nl-NL-Standard-A", "nl-NL-Standard-B-MALE": "nl-NL-Standard-B", "nl-NL-Standard-C-MALE": "nl-NL-Standard-C", "nl-NL-Standard-D-FEMALE": "nl-NL-Standard-D", "nl-NL-Standard-E-FEMALE": "nl-NL-Standard-E", "pl-PL-Standard-A-FEMALE": "pl-PL-Standard-A", "pl-PL-Standard-B-MALE": "pl-PL-Standard-B", "pl-PL-Standard-C-MALE": "pl-PL-Standard-C", "pl-PL-Standard-D-FEMALE": "pl-PL-Standard-D", "pl-PL-Standard-E-FEMALE": "pl-PL-Standard-E", "pt-BR-Standard-A-FEMALE": "pt-BR-Standard-A", "pt-PT-Standard-A-FEMALE": "pt-PT-Standard-A", "pt-PT-Standard-B-MALE": "pt-PT-Standard-B", "pt-PT-Standard-C-MALE": "pt-PT-Standard-C", "pt-PT-Standard-D-FEMALE": "pt-PT-Standard-D", "ru-RU-Standard-A-FEMALE": "ru-RU-Standard-A", "ru-RU-Standard-B-MALE": "ru-RU-Standard-B", "ru-RU-Standard-C-FEMALE": "ru-RU-Standard-C", "ru-RU-Standard-D-MALE": "ru-RU-Standard-D", "ru-RU-Standard-E-FEMALE": "ru-RU-Standard-E", "sk-SK-Standard-A-FEMALE": "sk-SK-Standard-A", "sv-SE-Standard-A-FEMALE": "sv-SE-Standard-A", "ta-IN-Standard-A-FEMALE": "ta-IN-Standard-A", "ta-IN-Standard-B-MALE": "ta-IN-Standard-B", "te-IN-Standard-A-FEMALE": "te-IN-Standard-A", "te-IN-Standard-B-MALE": "te-IN-Standard-B", "th-TH-Standard-A-FEMALE": "th-TH-Standard-A", "tr-TR-Standard-A-FEMALE": "tr-TR-Standard-A", "tr-TR-Standard-B-MALE": "tr-TR-Standard-B", "tr-TR-Standard-C-FEMALE": "tr-TR-Standard-C", "tr-TR-Standard-D-FEMALE": "tr-TR-Standard-D", "tr-TR-Standard-E-MALE": "tr-TR-Standard-E", "uk-UA-Standard-A-FEMALE": "uk-UA-Standard-A", "Kimberly": "", "Olivia": "<PERSON>", "Emma": "<PERSON>", "Brian": "<PERSON>", "Aria": "Aria", "Ivy": "Ivy"}