const md5 = require('md5');
require('dotenv').config();
import {
  REQUEST_STATUS,
  REQUEST_TYPE,
  KAFKA_TOPIC,
  AUDIO_TYPE,
} from '../constants';

const code = require('../errors/code');
const CustomError = require('../errors/CustomError');
const { getVoiceByCode } = require('./voice');
const {
  preCheckSynthesisApiRequest,
  getTextFromTemplateAndTags,
  countTextLength,
  getTags,
  // uploadBackgroundMusicFromLink,
} = require('./preprocessing');
const { createRequest, changeDomainUrl } = require('./request');
const { sendMessage } = require('./kafka/producer');

const { handleTtsSuccess, handleTtsFailure } = require('./ttsProcessing');
const { cacheTTSCachingAudioLink, checkCacheTTSCaching } = require('./cache');

import requestDao from '../daos/request';
import logger from '../utils/logger';
const { RandomFactory } = require('../utils/random');

// ADVISE: duplicate code
const getAwsZoneSynthesis = () => {
  const lengthAwsZones = AWS_ZONES_TTS_CACHING.length;
  const randomIndex = Math.floor(Math.random() * lengthAwsZones);
  const awsZone = AWS_ZONES_TTS_CACHING[randomIndex];
  return awsZone;
};

const handleCachingSynthesisRequest = async ({
  app,
  template,
  tags: tagsString,
  aesKey,
  voiceCode,
  outputType,
  responseType,
  backgroundMusic,
  speedRate,
  bitrate,
  sampleRate,
  callbackUrl,
  ip,
  serviceType,
  clientUserId,
  sessionId,
}) => {
  const requestId = RandomFactory.getGuid();
  const requestCreatedAt = new Date();
  const awsZoneSynthesis = getAwsZoneSynthesis();

  const {
    appId,
    userId,
    text,
    tags,
    packageCode,
    retentionPeriod,
    textLength,
  } = preCheckSynthesisApiRequest({
    app,
    tagsString,
    template,
    sessionId,
    requestId,
    aesKey,
  });

  const voice = await getVoiceByCode(voiceCode);
  if (!voice) throw new CustomError(code.INVALID_VOICE_CODE);

  if (!voice.cachingFunction)
    throw new CustomError(code.TTS_CACHING_NOT_SUPPORT_THIS_VOICE);
  if (sampleRate && !voice.sampleRates.includes(sampleRate))
    throw new CustomError(
      code.INVALID_SAMPLE_RATE,
      `This voice only support sample rate of ${JSON.stringify(
        voice.sampleRates,
      )}`,
    );

  const request = {
    ip,
    requestId,
    template,
    tags: tagsString,
    text,
    characters: textLength,
    audioType: AUDIO_TYPE.WAV,
    speed: speedRate,
    createdAt: requestCreatedAt,
    status: REQUEST_STATUS.IN_PROGRESS,
    voiceCode,
    bitrate,
    sampleRate: sampleRate ? sampleRate.toString() : voice.defaultSampleRate.toString(),
    retentionPeriod,
    app: appId,
    userId,
    callbackUrl,
    responseType,
    outputType,
    type: REQUEST_TYPE.API_CACHING,
    packageCode,
    aesKey,
    sessionId,
    serviceType,
    clientUserId,
    awsZoneSynthesis,
    proxyDomain: app.proxyDomain,
  };
  if (backgroundMusic) request.backgroundMusic = { link: backgroundMusic };

  await createRequest(request);

  logger.info('Created caching request', {
    ctx: 'CachingRequest',
    appId,
    sessionId,
    requestId,
  });
  const checkCacheExists = await checkCacheTTSCaching({
    requestId,
    template,
    tags: tagsString,
    voiceCode,
    speedRate,
    bitrate,
    sampleRate,
    backgroundMusic: request.backgroundMusic,
  });

  if (!checkCacheExists) {
    sendMessage(KAFKA_TOPIC.CACHING_SYNTHESIS_REQUEST, {
      value: {
        userId,
        requestId,
        sessionId,
        template,
        tags: JSON.stringify(tags),
        voiceCode,
        speedRate,
        bitrate,
        sampleRate,
        backgroundMusic,
        awsZoneSynthesis,
      },
    });
  }

  return request;
};

const handleCachingSynthesisSuccess = async ({
  requestId,
  statusCode,
  audioLink,
  totalTime,
  cachingMessage,
  getSynthesisRequestAt,
  startInvokeLambdaFunctionAt,
  endInvokeLambdaFunctionAt,
  isFromCache,
}) => {
  const endedAt = new Date();
  const getSynthesisResponseAt = Date.now();
  const request = await requestDao.findRequestById(requestId);
  const { proxyDomain } = request;

  const finalAudioLink = proxyDomain ? changeDomainUrl(audioLink, proxyDomain) : audioLink;

  const updateFields = {
    progress: 100,
    endedAt,
    cachingTime: totalTime,
    status: REQUEST_STATUS.SUCCESS,
    audioLink: finalAudioLink,
    cachingStatusCode: statusCode,
    cachingMessage,
    isFromCache,
    'timeProcess.getSynthesisRequestAt': getSynthesisRequestAt,
    'timeProcess.startInvokeLambdaFunctionAt': startInvokeLambdaFunctionAt,
    'timeProcess.endInvokeLambdaFunctionAt': endInvokeLambdaFunctionAt,
    'timeProcess.getSynthesisResponseAt': getSynthesisResponseAt,
  };
  if (isFromCache) updateFields.cachingRate = 1;

  const finalRequest = await requestDao.updateRequestById(requestId, updateFields);
  const preSendResponseAt = new Date();
  if (!isFromCache) {
    const { template, tags, speed: speedRate, voiceCode, bitrate, sampleRate, backgroundMusic } = finalRequest;
    cacheTTSCachingAudioLink({
      template,
      tags,
      voiceCode,
      speedRate,
      bitrate,
      sampleRate,
      backgroundMusic,
      audioLink,
    });
  }
  handleTtsSuccess(finalRequest, preSendResponseAt);
};

const handleCachingSynthesisFailure = async ({ requestId, statusCode, cachingMessage, cachingTime }) => {
  const finalRequest = await requestDao.updateRequestById(requestId, {
    cachingTime,
    cachingStatusCode: statusCode,
    cachingMessage,
    status: REQUEST_STATUS.FAILURE,
  });

  handleTtsFailure({
    request: finalRequest,
    errorCode: statusCode,
    errorMessage: cachingMessage,
  });
};

const getDetailDecryptedTemplates = ({ templates, tags: tagsString, aesKey }) => {
  const tags = getTags({ tagsString, aesKey });

  const detailDecryptedTemplates = templates.reduce(
    (acc, curr) => {
      let { texts, totalLength } = acc;
      const text = getTextFromTemplateAndTags({ template: curr.text, tags });
      totalLength += countTextLength(text);
      texts = [...texts, { id: curr.id, hashText: md5(text), length: text.length }];
      return { texts, totalLength };
    },
    {
      texts: [],
      totalLength: 0,
    },
  );

  return detailDecryptedTemplates;
};

module.exports = {
  handleCachingSynthesisRequest,
  handleCachingSynthesisSuccess,
  handleCachingSynthesisFailure,
  getDetailDecryptedTemplates,
};
