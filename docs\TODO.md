# De-duplicate code, using shared-lib

# Nếu code vbee-tts-api trùng với tts-api, thì xoá bên vbee-tts-api

# sync working deps to vbee-tts-api

- Some newers deps are used here. Can be sync safely to vbee-tts-api
- Compare the package.json, sync the newer versions

# update deps

axios <=0.29.0
Severity: high
Axios Cross-Site Request Forgery Vulnerability - https://github.com/advisories/GHSA-wf5p-g6vw-rhxx
axios Requests Vulnerable To Possible SSRF and Credential Leakage via Absolute URL - https://github.com/advisories/GHSA-jr5f-v2jv-69x6
fix available via `npm audit fix --force`
Will install axios@1.11.0, which is a breaking change
node_modules/axios

body-parser <=1.20.2
Severity: high
body-parser vulnerable to denial of service when url encoding is enabled - https://github.com/advisories/GHSA-qwcr-r2fm-qrc7
Depends on vulnerable versions of qs
fix available via `npm audit fix --force`
Will install express@4.21.2, which is outside the stated dependency range
node_modules/body-parser
express <=4.21.0 || 5.0.0-alpha.1 - 5.0.0
Depends on vulnerable versions of body-parser
Depends on vulnerable versions of cookie
Depends on vulnerable versions of path-to-regexp
Depends on vulnerable versions of qs
Depends on vulnerable versions of send
Depends on vulnerable versions of serve-static
node_modules/express

cookie <0.7.0
cookie accepts cookie name, path, and domain with out of bounds characters - https://github.com/advisories/GHSA-pxg6-pf52-xh8x
fix available via `npm audit fix --force`
Will install express@4.21.2, which is outside the stated dependency range
node_modules/cookie

mongoose 7.0.0-rc0 - 7.8.3
Severity: critical
Mongoose search injection vulnerability - https://github.com/advisories/GHSA-m7xq-9374-9rvx
Mongoose search injection vulnerability - https://github.com/advisories/GHSA-vg7j-7cwx-8wgw
fix available via `npm audit fix --force`
Will install mongoose@7.8.7, which is outside the stated dependency range
node_modules/mongoose

on-headers <1.1.0
on-headers is vulnerable to http response header manipulation - https://github.com/advisories/GHSA-76c9-3jph-rj3q
fix available via `npm audit fix --force`
Will install morgan@1.10.1, which is outside the stated dependency range
node_modules/on-headers
compression 1.0.3 - 1.8.0
Depends on vulnerable versions of on-headers
node_modules/compression
morgan 1.6.0 - 1.10.0
Depends on vulnerable versions of on-headers
node_modules/morgan

path-to-regexp <=0.1.11
Severity: high
path-to-regexp outputs backtracking regular expressions - https://github.com/advisories/GHSA-9wv6-86v2-598j
path-to-regexp contains a ReDoS - https://github.com/advisories/GHSA-rhx6-c78j-4q9w
fix available via `npm audit fix --force`
Will install express@4.21.2, which is outside the stated dependency range
node_modules/path-to-regexp

qs 6.7.0 - 6.7.2
Severity: high
qs vulnerable to Prototype Pollution - https://github.com/advisories/GHSA-hrpp-h998-j3pp
fix available via `npm audit fix --force`
Will install express@4.21.2, which is outside the stated dependency range
node_modules/qs

semver 7.0.0 - 7.5.1
Severity: high
semver vulnerable to Regular Expression Denial of Service - https://github.com/advisories/GHSA-c2qf-rxjj-qqgw
fix available via `npm audit fix --force`
Will install nodemon@3.1.10, which is a breaking change
node_modules/simple-update-notifier/node_modules/semver
simple-update-notifier 1.0.7 - 1.1.0
Depends on vulnerable versions of semver
node_modules/simple-update-notifier
nodemon 2.0.19 - 2.0.22
Depends on vulnerable versions of simple-update-notifier
node_modules/nodemon

send <0.19.0
send vulnerable to template injection that can lead to XSS - https://github.com/advisories/GHSA-m6fv-jmcg-4jfg
fix available via `npm audit fix --force`
Will install express@4.21.2, which is outside the stated dependency range
node_modules/send
serve-static <=1.16.0
Depends on vulnerable versions of send
node_modules/serve-static

15 vulnerabilities (6 low, 8 high, 1 critical)

To address issues that do not require attention, run:
npm audit fix

## package-lock

## Check and Remove unused

### ttsDao

checkCompletedIndex
saveAudio
checkCompletedIndexInRedis
deleteTTSByRequestId
updateCachePhrases

### requestDao

saveAudioLink

getSynthesisTime
getTotalTtsByRequestIdInRedis
getTotalSuccessTtsByRequestIdInRedis
updateTtsInRequestInRedis
getAudios
countRealTts
updateFailureTTS

### isolate to Caching

processTtsDubbingToJoinAudios
