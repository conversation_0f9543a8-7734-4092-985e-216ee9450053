import jwt from 'jsonwebtoken';
import configs from '../configs';
const { IAM_VALID_CLIENT_IDS } = configs;
const camelCaseKeys = require('camelcase-keys');
const CustomError = require('../errors/CustomError');
const errorCodes = require('../errors/code');

const verifyAccessToken = async (accessToken) => {
  try {
    let data = jwt.verify(accessToken, IAM_PUBLIC_KEY);
    data = camelCaseKeys(data);
    const { azp: clientId } = data;

    if (!IAM_VALID_CLIENT_IDS.includes(clientId)) throw new CustomError(errorCodes.UNAUTHORIZED);

    return data;
  } catch (error) {
    throw new CustomError(errorCodes.UNAUTHORIZED);
  }
};

const verifyAccessTokenAPI = async (appId, accessToken) => {
  try {
    const app = APPS[appId];
    if (!app) throw new CustomError(errorCodes.APP_NOT_FOUND);
    if (!app.active) throw new CustomError(errorCodes.APP_NOT_ACTIVATED);

    const { secretKey, token: appToken } = app;
    if (!secretKey) throw new CustomError(errorCodes.UNAUTHORIZED);

    if (accessToken !== appToken) throw new CustomError(errorCodes.UNAUTHORIZED);
    jwt.verify(accessToken, secretKey);
    return app;
  } catch (error) {
    throw new CustomError(error.errorCode || errorCodes.UNAUTHORIZED);
  }
};

module.exports = {
  verifyAccessToken,
  verifyAccessTokenAPI,
};
