# Build stage
FROM node:20.19-alpine AS builder
WORKDIR /app

# Copy package files from local directory to inside the image (WORKDIR)
COPY package*.json ./
COPY tsconfig*.json ./
COPY .npmrc ./

# Creating .npmrc on-the-fly to access private repo inside vbee-holding org.
# Then install ALL dependencies (including devDependencies for TypeScript compilation)
# When we provide package-lock.json with the project. change to use `npm ci`
RUN --mount=type=secret,id=npm_token \
  export NPM_TOKEN=$(cat /run/secrets/npm_token) && \
  npm install && \
  rm .npmrc

# Copy source code and configuration files
COPY src/ ./src/

# Build TypeScript application
RUN npm run build

# Production stage
FROM node:20.19-alpine AS production
WORKDIR /app

# Copy package files for production install
COPY package*.json ./
COPY .npmrc ./

# Creating .npmrc on-the-fly to access private repo inside vbee-holding org.
# Install only production dependencies
# When we provide package-lock.json with the project. change to use `npm ci --only=production`
RUN --mount=type=secret,id=npm_token \
  export NPM_TOKEN=$(cat /run/secrets/npm_token) && \
  npm install --omit=dev && \
  rm .npmrc

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

# Start the application
CMD ["node", "dist/index.js"]
