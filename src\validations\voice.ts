const { Joi, validate } = require('express-validation')
import { VOICE_PROVIDER } from '../constants'

const createVoice = {
  body: Joi.object({
    code: Joi.string().trim().required(),
    name: Joi.string().trim().required(),
    image: Joi.string().trim().uri().optional(),
    gender: Joi.string().valid('male', 'female', 'other').required(),
    languageCode: Joi.string().trim().required(),
    provider: Joi.string()
      .valid(...VOICE_PROVIDER)
      .required(),
    squareImage: Joi.string().trim().uri().optional(),
    roundImage: Joi.string().trim().uri().optional(),
    sampleRates: Joi.array().items(Joi.number()).required(),
    defaultSampleRate: Joi.number().required(),
    global: Joi.boolean().required(),
    synthesisFunction: Joi.string().trim().optional(),
  }),
}

module.exports = {
  createVoiceValidation: validate(createVoice, { keyByField: true }),
}
