# Express Routing in the Repository

Entry Point (src/index.js)

Prefix /api/v1

- GET /tts ==> BO debug
- /tts => apiSynthesis
- /tts/mobifone => apiSynthesisMobifone
- /tts/caching => cachingSynthesis
- /tts/caching/text-length => countTextLength

- /tts-gate-api/callback

- /tts/dubbing => handleDubbing

- /requests/:sessionId

- /voices => createVoice

- /uptime => healthCheckController.getUptime
