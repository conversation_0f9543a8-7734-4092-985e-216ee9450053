import { Router } from 'express'
const router = Router()

const asyncMiddleware = require('../middlewares/async')
const { authAPI } = require('../middlewares/auth')

// --------- API CORE
const synthesisApiController = require('../controllers/api')
const cachingRequestController = require('../controllers/caching')
const { cachingRequestValidate, countTextLengthValidate } = require('../validations/caching')
const { synthesisApiValidate } = require('../validations/api')
router.post('/tts', authAPI, synthesisApiValidate, asyncMiddleware(synthesisApiController.apiSynthesis))
router.post('/tts/mobifone', authAPI, asyncMiddleware(synthesisApiController.apiSynthesisMobifone))
router.post('/tts/caching', authAPI, cachingRequestValidate, asyncMiddleware(cachingRequestController.cachingSynthesis))
router.post('/tts/caching/text-length', authAPI, countTextLengthValidate, asyncMiddleware(cachingRequestController.countTextLength))

// --------- CALLBACK
const callbackController = require('../controllers/callback')
router.post('/tts-gate-api/callback', asyncMiddleware(callbackController.apiCallbackResponse))

// --------- DUBBING
const dubbingController = require('../controllers/dubbing')
const { dubbingApiValidation } = require('../validations/dubbing')
router.post('/dubbing', authAPI, dubbingApiValidation, asyncMiddleware(dubbingController.handleDubbing))

// --------- HEALTHCHECK
const healthCheckController = require('../controllers/healthCheck')
router.get('/uptime', asyncMiddleware(healthCheckController.getUptime))

// --------- REQUEST
const requestController = require('../controllers/request')
router.get('/requests/:sessionId', asyncMiddleware(requestController.getRequestFromSessionId))

// --------- BACKOFFICE DEBUG REQUEST
const ttsController = require('../controllers/tts')
router.get('/tts', asyncMiddleware(ttsController.getTts)) // NOTE: This API is used for back office to debug tts request

// --------- VOICE
const voiceController = require('../controllers/voice')
router.post('/voices', authAPI, asyncMiddleware(voiceController.createVoice))

const initRoute = (app) => {
  const appBaseRoute = '/api/v1'
  app.use(appBaseRoute, router)

  logger.info(
    `Routes: Init successfully
${router.stack.map((layer) => appBaseRoute + layer.route.path).join('\n')}
`,
  )
}

export { initRoute }
