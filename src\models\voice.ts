const mongoose = require('mongoose')
import { VOICE_PROVIDER, VOICE_LEVEL } from '../constants'

const voiceSchema = new mongoose.Schema(
  {
    code: String,
    name: String,
    image: String,
    gender: { type: String, enum: ['male', 'female'] },
    languageCode: String,
    type: String,
    provider: {
      type: String,
      enum: Object.values(VOICE_PROVIDER),
    },
    squareImage: String,
    roundImage: String,
    demo: String,
    rank: { type: Number, default: 999 },
    features: [String],
    styles: [String],
    sampleRates: [Number],
    defaultSampleRate: Number,
    cachingFunction: String,
    synthesisFunction: String,
    active: Boolean,
    global: Boolean,
    level: {
      type: String,
      enum: Object.values(VOICE_LEVEL),
    },
    version: String,
    beta: Boolean,
    hasDubbing: { type: Boolean, default: false },
    cloudRun: {
      url: String,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
)

module.exports = mongoose.model('Voice', voiceSchema)
