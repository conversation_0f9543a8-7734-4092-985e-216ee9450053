import { it, expect, describe, assert } from 'vitest';

import jwt from 'jsonwebtoken';

// use dev token, so I guess there is no harm for this test
describe('jwt 9.0.2 using dev environment token', () => {
  it.skip('verify access token', () => {
    const authToken = `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE2Nzc2NTQ0MDB9.YwO9ipHG-GtyyhkfcEKAr0Ym4K358j1nfMKZkl8KdIY`;
    const IAM_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAj7bt4+LunsbWLDAut8eE6uSFYIhl8HGNSDuovN9TSJArS3d8OtkDRWIiYB7ggnnBoZxV2SIkRX6A+pfgy0DUHIg1Qdbgdn24+0idxrNu+wFL1rQm8DlnWCRDfzh5iQfrEEGWLUVmYLLYna5+Uml+w/9hTPDj254YGqdfi4ByWQFr4AAkEq4OvruHxYnHjnlkKQ9+9J4WgVheN4F1sHzQckfO+edYIBJpqyadfvSjD01qhTxT1n0Mv5Ly4fXPTLqagRktGCDeyr7Zh6niblAYWg4N8INTLUPKrr5yMWIaQwzICjR18LnE3Z1gSHVSAA2VOcJuYg4VEHWfq9ivupnGKwIDAQAB
-----END PUBLIC KEY-----`;

    try {
      const decoded = jwt.verify(authToken, IAM_PUBLIC_KEY);
      expect(decoded.toString()).toBeTruthy();
    } catch (error) {
      assert.fail(error.toString());
    }
  });

  it('verify access token for API', () => {
    const authToken = `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE2Nzc2NTQ0MDB9.YwO9ipHG-GtyyhkfcEKAr0Ym4K358j1nfMKZkl8KdIY`;
    const secretKey = `b78b8643-6db3-4de7-8859-37014498ae65`;

    try {
      const decoded = jwt.verify(authToken, secretKey);
      expect(decoded).toBeTruthy();
    } catch (error) {
      assert.fail(error);
    }
  });
});
