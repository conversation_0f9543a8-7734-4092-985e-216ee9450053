const { Joi, validate } = require('express-validation')
import { VALID_SPEED, AUDIO_TYPE, RESPONSE_TYPE, TTS_CORE_COMPUTE_PLATFORM } from '../constants'

const dubbingApi = {
  body: Joi.object({
    appId: Joi.string().trim().required(),
    title: Joi.string().trim().required(),
    subtitleLink: Joi.string().uri().trim().required(),
    audioDomainType: Joi.string().optional(),
    voiceCode: Joi.string().trim().required(),
    callbackUrl: Joi.string()
      .uri()
      .trim()
      .when('responseType', {
        is: Joi.string().valid(RESPONSE_TYPE.INDIRECT),
        then: Joi.required(),
        otherwise: Joi.optional(),
      }),
    callbackUpdateProgressUrl: Joi.string().uri().trim().optional(),
    speedRate: Joi.number().min(VALID_SPEED.MIN).max(VALID_SPEED.MAX).optional(),
    audioType: Joi.string()
      .valid(...Object.values(AUDIO_TYPE))
      .optional(),
    sampleRate: Joi.number().optional(),
    retentionPeriod: Joi.number().optional(),
    clientUserId: Joi.string().trim().optional(),
    sessionId: Joi.string().optional(),
    responseType: Joi.string()
      .valid(...Object.values(RESPONSE_TYPE))
      .optional(),
    synthesisCcr: Joi.number().optional(),
    sentencesVoiceCode: Joi.object()
      .pattern(Joi.string().required(), Joi.array().items(Joi.number()).required())
      .optional(),
    synthesisComputePlatform: Joi.string()
      .valid(...Object.values(TTS_CORE_COMPUTE_PLATFORM))
      .optional(),
  }),
}

module.exports = {
  dubbingApiValidation: validate(dubbingApi, { keyByField: true }),
}
