import { REQUEST_TYPE } from '../constants'

const mongoose = require('mongoose')
import configs from '../configs'

const appSchema = new mongoose.Schema(
  {
    _id: String,
    name: String,
    token: String,
    expiresAt: Date,
    secretKey: String,
    active: { type: Boolean, default: true },
    server: { type: String, default: configs.SERVER_ENV },
    allowRequestTypes: [{ type: String, enum: Object.values(REQUEST_TYPE) }],
    proxyDomain: String,
    voiceCodes: [String],
  },
  {
    _id: false,
    versionKey: false,
    timestamps: true,
  },
)

module.exports = mongoose.model('App', appSchema)
