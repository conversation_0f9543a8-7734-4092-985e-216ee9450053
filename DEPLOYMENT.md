# TTS API Deployment Guide

## Prerequisites

- Node.js 20.19 or higher
- Dock<PERSON> and Docker Compose
- NPM token for @vbee-holding packages

## Deployment Options

### 1. Local Development

**Direct Node.js:**

```bash
npm install
npm run dev
```

**With Docker:**

```bash
# Create NPM token file
echo 'your_github_token_here' > .npm_token

# Start development environment
.\docker-build.ps1 run dev
```

### 2. Production Deployment

**Docker (Recommended):**

```bash
# Build production image
docker build --secret id=npm_token,src=.npm_token -t tts-api:latest .
```

**Direct Node.js:**

```bash
npm ci --only=production
npm run build
npm start
```

## Build Process

### TypeScript Compilation

The application automatically builds TypeScript before starting:

1. **Development**: Uses `ts-node-dev` for hot reload
2. **Production**: Compiles to JavaScript in `dist/` folder
3. **Docker**: Multi-stage build with optimized production image

### Build Commands

```bash
# Manual build
npm run build

# Development with auto-build
npm run dev

# Production with auto-build
npm start
```

## Health Checks

The application provides health check endpoints:

- TODO: **Health**: `GET /health`
- TODO: **Status**: `GET /status`
- TODO: **Metrics**: `GET /metrics` (if enabled)

## Monitoring

### Logging

- Uses structured logging with context
- Configurable log levels
- JSON format for production

## Security Considerations

### Docker Security

- TODO: Non-root user execution
- Minimal base image (Alpine Linux)
- Secret management for sensitive data
- Network isolation

### Application Security

- Environment variable configuration
- Private key management
- Authentication token validation
- Input validation and sanitization

## Maintenance & Performance Optimization

### Regular Tasks

- Dependency updates
- Security patches
- Log rotation
- Performance monitoring

### Application Level

- Connection pooling for databases
- Caching strategies with Redis
- Async/await optimization
- Memory leak prevention

### Infrastructure Level

- Container resource limits
- Database indexing
- CDN for static assets
- Load balancing

## Backup and Recovery

### Database Backup

- MongoDB regular backups
- Redis persistence configuration
- Point-in-time recovery

### Application Backup

- Configuration management
- Docker image versioning

### Monitoring Alerts

- Application errors
- Database connectivity
- Memory usage
- Response time degradation
