require('dotenv').config();
import configs from '../../configs';
const { getAllApps } = require('../../daos/app');
import logger from '../../utils/logger';

const initApps = async () => {
  try {
    const apps = (await getAllApps()) || [];

    apps.forEach((app) => {
      APPS[app._id] = app; // ADVISE: cache to memory? Should use CacheManager
    });
  } catch (error) {
    logger.error(error, { ctx: 'InitApps' });
  } finally {
    setTimeout(initApps, configs.GET_APPS_INTERVAL);
  }

  logger.info('Apps: Init successfully,', { ctx: 'Service.Apps' });
};

module.exports = { initApps };
