const { Joi, validate } = require('express-validation')
import { RESPONSE_TYPE, OUTPUT_TYPE, VALID_BIT_RATE, VALID_SPEED } from '../constants'

const cachingRequest = {
  body: Joi.object({
    appId: Joi.string().trim().required(),
    template: Joi.string().required(),
    tags: Joi.string().required(),
    aesKey: Joi.string().optional(),
    voiceCode: Joi.string().required(),
    outputType: Joi.string()
      .valid(...Object.values(OUTPUT_TYPE))
      .optional(),
    responseType: Joi.string()
      .valid(...Object.values(RESPONSE_TYPE))
      .optional(),
    backgroundMusic: Joi.string().uri().trim().optional(),
    speedRate: Joi.number().min(VALID_SPEED.MIN).max(VALID_SPEED.MAX).optional(),
    sampleRate: Joi.number().optional(),
    bitrate: Joi.number()
      .valid(...VALID_BIT_RATE)
      .optional(),
    callbackUrl: Joi.string()
      .uri()
      .trim()
      .when('responseType', {
        is: Joi.string().valid(RESPONSE_TYPE.INDIRECT),
        then: Joi.required(),
        otherwise: Joi.optional(),
      }),
    sessionId: Joi.string().optional(),
    serviceType: Joi.string().max(50).optional(),
    clientUserId: Joi.string().max(100).optional(),
    clientSendRequestAt: Joi.string().optional(),
  }),
}

const template = Joi.object().keys({
  id: Joi.string().required(),
  text: Joi.string().required(),
})

const countTextLength = {
  body: Joi.object({
    appId: Joi.string().required(),
    templates: Joi.array().min(1).items(template).required(),
    aesKey: Joi.string().required(),
    tags: Joi.string().required(),
  }),
}

module.exports = {
  cachingRequestValidate: validate(cachingRequest, { keyByField: true }),
  countTextLengthValidate: validate(countTextLength, { keyByField: true }),
}
