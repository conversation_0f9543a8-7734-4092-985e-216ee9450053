const moment = require('moment')
const daoUtils = require('./utils')
const Request = require('../models/request')
import { REQUEST_STATUS, REDIS_KEY_PREFIX, REDIS_KEY_TTL, TTS_STATUS } from '../constants'
import logger from '../utils/logger'
import Caching from '../services/caching'
const CustomError = require('../errors/CustomError')
const code = require('../errors/code')
const { getTtsFromRequestIdInRedis } = require('./tts')

const createRequest = async (requestInfo) => {
  const request = await Request.create(requestInfo);
  return request;
};

const findRequestById = async (requestId) => {
  const [request] = await Request.aggregate([
    {
      $match: { _id: requestId },
    },
    {
      $addFields: { numberOfSentences: { $size: '$sentences' } },
    },
    {
      $lookup: {
        from: 'voices',
        localField: 'voiceCode',
        foreignField: 'code',
        as: 'voice',
      },
    },
    { $unwind: { path: '$voice', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'languages',
        localField: 'voice.languageCode',
        foreignField: 'code',
        as: 'voice.language',
      },
    },
    { $unwind: { path: '$voice.language', preserveNullAndEmptyArrays: true } },
    { $project: { voiceCode: 0 } },
  ]);

  return request;
};

const findRequestByIdInRedis = async (requestId) => {
  try {
    const requestKey = `${REDIS_KEY_PREFIX.REQUEST}_${requestId}`;
    const request = await Caching.RedisRepo.get(requestKey);
    if (!request) throw new CustomError(code.REQUEST_NOT_FOUND);

    const requestObj = JSON.parse(request);

    return requestObj;
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis', requestId });
    return {};
  }
};

const saveAudioLink = async (requestId, audioLink) => {
  const endedAt = new Date();
  const request = await Request.findByIdAndUpdate(
    requestId,
    { audioLink, status: REQUEST_STATUS.SUCCESS, endedAt },
    { new: true },
  ).lean();
  return request;
};

const saveAudioLinkInRedis = async ({ requestId, audioLink, timestampArr }) => {
  const endedAt = new Date();
  const request = await updateRequestByIdInRedis(requestId, {
    audioLink,
    status: REQUEST_STATUS.SUCCESS,
    endedAt,
    timestampWords: timestampArr,
  });

  return request;
};

const getDetailSentencesByRequestId = async (requestId) => {
  const [request] = await Request.aggregate([
    { $match: { _id: requestId } },
    { $unwind: '$sentences' },
    {
      $lookup: {
        from: 'voices',
        localField: 'sentences.voiceCode',
        foreignField: 'code',
        as: 'sentences.voice',
      },
    },
    { $unwind: '$sentences.voice' },
    {
      $lookup: {
        from: 'languages',
        localField: 'sentences.voice.languageCode',
        foreignField: 'code',
        as: 'sentences.voice.language',
      },
    },
    { $unwind: '$sentences.voice.language' },
    {
      $group: {
        _id: '$_id',
        sentences: { $push: '$sentences' },
      },
    },
  ]);

  return request.sentences;
};

const updateRequestById = async (requestId, updateFields) => {
  const request = await Request.findByIdAndUpdate(requestId, updateFields, {
    new: true,
    omitUndefined: true,
  }).lean();
  return request;
};

const updateRequestByIdInRedis = async (requestId, updateFields) => {
  try {
    const requestKey = `${REDIS_KEY_PREFIX.REQUEST}_${requestId}`;

    let request = await Caching.RedisRepo.get(requestKey);
    if (!request) throw new CustomError(code.REQUEST_NOT_FOUND);

    request = JSON.parse(request);
    request = { ...request, ...updateFields };

    await Caching.RedisRepo.set(requestKey, JSON.stringify(request), REDIS_KEY_TTL.SYNTHESIS_REQUEST);

    return request;
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis', requestId });
    return {};
  }
};

const updateManyRequestsByIds = async (requestIds, updateFields) => {
  await Request.updateMany(
    {
      _id: { $in: requestIds },
    },
    updateFields,
  );
};

const checkRequestExists = async (condition) => {
  const isExists = await daoUtils.checkExists(Request, condition);
  return isExists;
};

const findRequest = async (condition, fields?) => {
  const request = await daoUtils.findOne(Request, condition, fields);
  return request;
};

const updateTtsStatusInRequest = async ({ requestId, ttsId, status }) => {
  const request = await findRequestByIdInRedis(requestId);
  const { ttsIds = [] } = request || {};
  const ttsFailedIndex = ttsIds.findIndex((tts) => tts.ttsId === ttsId);

  if (ttsFailedIndex !== -1) {
    ttsIds[ttsFailedIndex].status = status;
    const newRequest = { ...request, ttsIds };
    await updateRequestByIdInRedis(requestId, newRequest);
  }
};

const updateFinalRequestFromRedisToDB = async (requestId, cacheRequest) => {
  const {
    endedAt,
    progress,
    status,
    sentenceTokenizerDuration,
    t2aDuration,
    synthesisDuration,
    joinAudioDuration,
    audioLink,
    error,
    errorCode,
    firstStreamAudioDuration,
  } = cacheRequest;

  await updateRequestById(requestId, {
    endedAt,
    progress,
    status,
    sentenceTokenizerDuration,
    t2aDuration,
    synthesisDuration,
    joinAudioDuration,
    audioLink,
    error,
    errorCode,
    firstStreamAudioDuration,
  });
};

const updateTtsInRequestInRedis = async ({ requestId, ttsId, status }) => {
  const request = await findRequestByIdInRedis(requestId);
  const { ttsIds = [] } = request || {};
  const ttsIndex = ttsIds.findIndex((tts) => tts.ttsId === ttsId);
  if (ttsIndex < 0) return;
  ttsIds[ttsIndex].status = status;

  const newRequest = { ...request, ttsIds };
  await updateRequestByIdInRedis(requestId, newRequest);
};

const saveTimeProcessInRedis = async (timeProcess, cachingTime) => {
  try {
    const { processDuration, startInvokeLambdaFunctionAt, endInvokeLambdaFunctionAt } = timeProcess;
    const invokeLambdaDuration = new Date(endInvokeLambdaFunctionAt) - new Date(startInvokeLambdaFunctionAt);
    const systemProcessDuration = processDuration - invokeLambdaDuration;

    // 10 suffix keys 0 -> 9
    const suffixKeyRedis = moment(endInvokeLambdaFunctionAt).minutes() % 10;
    const totalProcessDurationKey = `${REDIS_KEY_PREFIX.TOTAL_TIME_PROCESS_DURATION}${suffixKeyRedis}`;
    const totalProcessCoreDurationKey = `${REDIS_KEY_PREFIX.TOTAL_PROCESS_CORE_DURATION}${suffixKeyRedis}`;
    const totalProcessSystemDurationKey = `${REDIS_KEY_PREFIX.TOTAL_PROCESS_SYSTEM_DURATION}${suffixKeyRedis}`;
    const totalProcessInvokeLambdaDurationKey = `${REDIS_KEY_PREFIX.TOTAL_PROCESS_INVOKE_LAMBDA_DURATION}${suffixKeyRedis}`;
    const totalRequestKey = `${REDIS_KEY_PREFIX.TOTAL_REQUEST}${suffixKeyRedis}`;

    await Caching.GlobalCounter.increaseBy(totalProcessDurationKey, processDuration || 0);
    await Caching.GlobalCounter.increaseBy(totalProcessCoreDurationKey, cachingTime || 0);
    await Caching.GlobalCounter.increaseBy(totalProcessSystemDurationKey, systemProcessDuration || 0);
    await Caching.GlobalCounter.increaseBy(totalProcessInvokeLambdaDurationKey, invokeLambdaDuration || 0);
    await Caching.GlobalCounter.increase(totalRequestKey);
  } catch (error) {
    logger.error(error, { ctx: 'RequestRedis' });
  }
};

const checkDoneTts = async (requestId) => {
  const ttsList = await getTtsFromRequestIdInRedis(requestId);
  if (!ttsList || ttsList.length === 0) return true;
  const isNotDone = ttsList.some((tts) => tts?.status === TTS_STATUS.IN_PROGRESS);

  return !isNotDone;
};

export default {
  createRequest,
  findRequestById,
  findRequestByIdInRedis,
  saveAudioLink,
  saveAudioLinkInRedis,
  getDetailSentencesByRequestId,
  updateRequestById,
  updateRequestByIdInRedis,
  updateManyRequestsByIds,
  checkRequestExists,
  checkDoneTts,
  findRequest,
  updateTtsStatusInRequest,
  updateFinalRequestFromRedisToDB,
  updateTtsInRequestInRedis,
  saveTimeProcessInRedis,
};
